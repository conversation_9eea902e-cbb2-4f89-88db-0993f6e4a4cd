import { useState } from 'react';
import { Bug, ChevronDown, ChevronUp, Info, AlertCircle, Activity, MessageSquare, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface DebugPanelProps {
  onTriggerInformation: (message: string) => void;
  onTriggerError: (message: string) => void;
  onTriggerBusy: () => void;
  onTriggerIdle: () => void;
  onTriggerCriticalError: (message: string) => void;
  connectionState: string;
  isLoading: boolean;
  messagesCount: number;
}

export const DebugPanel = ({
  onTriggerInformation,
  onTriggerError,
  onTriggerBusy,
  onTriggerIdle,
  onTriggerCriticalError,
  connectionState,
  isLoading,
  messagesCount
}: DebugPanelProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [infoMessage, setInfoMessage] = useState('这是一个测试信息通知');
  const [errorMessage, setErrorMessage] = useState('这是一个测试错误消息');
  const [criticalErrorMessage, setCriticalErrorMessage] = useState('这是一个严重错误');

  // 只在开发环境中显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-32 right-4 z-[100] max-w-md">
      <Card className={cn(
        "border-orange-200 bg-orange-50/80 backdrop-blur-sm shadow-lg",
        "transition-all duration-300 ease-in-out",
        isExpanded ? "w-96" : "w-auto"
      )}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bug className="h-4 w-4 text-orange-600" />
              <CardTitle className="text-sm text-orange-800">开发调试面板</CardTitle>
              <Badge variant="outline" className="text-xs border-orange-300 text-orange-700">
                DEV
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 p-0 text-orange-600 hover:bg-orange-100"
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            </Button>
          </div>
        </CardHeader>

        {isExpanded && (
          <CardContent className="space-y-4">
            {/* 连接状态信息 */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-orange-800">系统状态</Label>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center gap-1">
                  <Activity className="h-3 w-3" />
                  <span>连接: {connectionState}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  <span>消息: {messagesCount}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Zap className="h-3 w-3" />
                  <span>加载: {isLoading ? '是' : '否'}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Information 消息测试 */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-orange-800 flex items-center gap-1">
                <Info className="h-3 w-3" />
                Information 通知测试
              </Label>
              <div className="space-y-2">
                <Input
                  value={infoMessage}
                  onChange={(e) => setInfoMessage(e.target.value)}
                  placeholder="输入信息通知内容"
                  className="text-xs h-8"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onTriggerInformation(infoMessage)}
                  className="w-full h-7 text-xs"
                >
                  触发 Information 通知
                </Button>
              </div>
            </div>

            <Separator />

            {/* 错误消息测试 */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-orange-800 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                错误消息测试
              </Label>
              <div className="space-y-2">
                <Input
                  value={errorMessage}
                  onChange={(e) => setErrorMessage(e.target.value)}
                  placeholder="输入错误消息内容"
                  className="text-xs h-8"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onTriggerError(errorMessage)}
                  className="w-full h-7 text-xs"
                >
                  触发 Error 消息
                </Button>
              </div>
            </div>

            <Separator />

            {/* 状态控制 */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-orange-800">状态控制</Label>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onTriggerBusy}
                  className="h-7 text-xs"
                  disabled={isLoading}
                >
                  设为忙碌
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onTriggerIdle}
                  className="h-7 text-xs"
                  disabled={!isLoading}
                >
                  设为空闲
                </Button>
              </div>
            </div>

            <Separator />

            {/* 严重错误测试 */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-orange-800 text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                严重错误测试
              </Label>
              <div className="space-y-2">
                <Input
                  value={criticalErrorMessage}
                  onChange={(e) => setCriticalErrorMessage(e.target.value)}
                  placeholder="输入严重错误消息"
                  className="text-xs h-8"
                />
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => onTriggerCriticalError(criticalErrorMessage)}
                  className="w-full h-7 text-xs"
                >
                  触发 Critical Error
                </Button>
              </div>
            </div>

          </CardContent>
        )}
      </Card>
    </div>
  );
};