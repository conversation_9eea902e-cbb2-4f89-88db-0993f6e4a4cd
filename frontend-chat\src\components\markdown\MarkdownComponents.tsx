import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { ChartRenderer } from "@/components/chart/ChartRenderer";

interface MarkdownComponentProps {
  children?: React.ReactNode;
  [key: string]: unknown;
}

interface CodeBlockProps extends MarkdownComponentProps {
  className?: string;
}

export const markdownComponents = {
  a: ({ children, href, ...props }: MarkdownComponentProps) => (
    <a href={href as string} target="_blank" {...props}>{children}</a>
  ),
  table: ({ children, ...props }: MarkdownComponentProps) => (
    <div>
      <Table {...props}>{children}</Table>
    </div>
  ),
  thead: ({ children, ...props }: MarkdownComponentProps) => (
    <TableHeader {...props}>{children}</TableHeader>
  ),
  tbody: ({ children, ...props }: MarkdownComponentProps) => (
    <TableBody {...props}>{children}</TableBody>
  ),
  tr: ({ children, ...props }: MarkdownComponentProps) => (
    <TableRow {...props}>{children}</TableRow>
  ),
  th: ({ children, ...props }: MarkdownComponentProps) => (
    <TableHead {...props}>{children}</TableHead>
  ),
  td: ({ children, ...props }: MarkdownComponentProps) => (
    <TableCell {...props} title={typeof children === 'string' ? children : undefined}>{children}</TableCell>
  ),
  code: ({ className, children, ...props }: CodeBlockProps) => {
    // Check if it's a chart-json code block
    if (className === 'language-chart-js-json' && typeof children === 'string') {
      // Check if JSON appears incomplete (common indicators)
      const trimmed = children.trim();
      const isIncomplete = (
        !trimmed.startsWith('{') ||
        !trimmed.endsWith('}') ||
        (trimmed.split('{').length !== trimmed.split('}').length)
      );

      // If JSON appears incomplete, show loading state
      if (isIncomplete) {
        return (
          <div className="my-2 px-4 py-12 border border-border rounded-lg bg-muted/50">
            <div className="flex items-center justify-center space-x-2 text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span className="text-sm">📊 图表渲染中...</span>
            </div>
          </div>
        );
      }

      try {
        const config = JSON.parse(children);
        return (
          <div className="my-2 p-2 border border-border rounded-lg bg-muted/50">
            <ChartRenderer config={config} />
          </div>
        );
      } catch (error) {
        return (
          <div className="p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950/20 my-4">
            <p className="text-red-600 dark:text-red-400 text-sm">
              图表渲染失败: {error instanceof Error ? error.message : 'Invalid JSON'}
            </p>
          </div>
        );
      }
    }
    
    // Default inline code styling
    if (!className) {
      return (
        <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold" {...props}>
          {children}
        </code>
      );
    }
    
    // Default code block styling
    return (
      <pre className="overflow-x-auto bg-muted rounded-lg p-4 my-4">
        <code className={className} {...props}>
          {children}
        </code>
      </pre>
    );
  }
};