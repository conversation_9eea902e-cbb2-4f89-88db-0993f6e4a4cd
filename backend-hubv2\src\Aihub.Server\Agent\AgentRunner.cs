using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Nodes;
using Aihub.Server.Agent.ChatAgents;
using Aihub.Server.Contexts;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Exceptions;
using Aihub.Server.Models.Dto;
using Aihub.Server.Scrutors;
using Aihub.Server.Services;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Agent;

public class AgentRunner : ITransientService
{

    private readonly SubAgentAndToolCallTimesCounter _subAgentAndToolCallTimesCounter;
    private readonly AgentKeyTypeAccessor _agentKeyTypeAccessor;
    private readonly ICurrentApplicationAccessor _currentApplicationAccessor;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ChatHubWrapper _chatHubWrapper;
    private readonly ILogger<AgentRunner> _logger;

    public AgentRunner(
        SubAgentAndToolCallTimesCounter subAgentAndTool<PERSON>allTimesCounter,
        AgentKeyTypeAccessor agentKeyTypeAccessor,
        ICurrentApplicationAccessor currentApplicationAccessor,
        IServiceScopeFactory serviceScopeFactory,
        ChatHubWrapper chatHubWrapper,
        ILogger<AgentRunner> logger)
    {
        _subAgentAndToolCallTimesCounter = subAgentAndToolCallTimesCounter;
        _agentKeyTypeAccessor = agentKeyTypeAccessor;
        _currentApplicationAccessor = currentApplicationAccessor;
        _serviceScopeFactory = serviceScopeFactory;
        _chatHubWrapper = chatHubWrapper;
        _logger = logger;
    }

    public Task RunAsync(string ticketCode, Guid sessionId, CancellationToken cancellationToken)
    {
        Task.Run(() => InternalRunAsync(ticketCode, sessionId, cancellationToken));
        return Task.CompletedTask;
    }

    private async Task InternalRunAsync(string ticketCode, Guid sessionId, CancellationToken cancellationToken)
    {
        using var scope = _serviceScopeFactory.CreateScope();

        // 初始化会话上下文
        var sessionContextService = scope.ServiceProvider.GetRequiredService<SessionContextService>();
        await sessionContextService.ManualInitializeWith(ticketCode, sessionId);

        // 初始化计数器
        var subAgentAndToolCallTimesCounter = scope.ServiceProvider.GetRequiredService<SubAgentAndToolCallTimesCounter>();
        subAgentAndToolCallTimesCounter.AssociateWith(_subAgentAndToolCallTimesCounter);

        var dbContextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<AihubDbContext>>();
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();

        try
        {

            var application = await dbContext.Applications.FirstOrDefaultAsync(x => x.Id == _currentApplicationAccessor.GetApplicationId());
            if (application is null) throw new Exception("Application not found");

            var agentOption = await dbContext.AgentOptions.FirstOrDefaultAsync(x => x.Id == application.AgentOptionId);
            if (agentOption is null) throw new Exception("AgentOption not found");

            // 设置当前代理上下文
            var agentContextManager = scope.ServiceProvider.GetRequiredService<AgentContextManager>();
            agentContextManager.InitializeWith(sessionId, agentOption.Id);

            var agent = CreateChatAgent(scope, agentOption.Key);
            await agent.RunAsync(cancellationToken);


        }
        catch (OperationCanceledException)
        {
            await _chatHubWrapper.SendCanceled();
        }
        catch (MaxToolCallLoopTimesReachedException ex)
        {
            _logger.LogInformation(ex, "Max tool call loop times reached");
            var message = "还希望我继续下去吗？";
            var messageId = Guid.CreateVersion7();
            await dbContext.StoredMessages.AddAsync(new StoredMessage
            {
                Id = messageId,
                SessionId = sessionId,
                Content = message,
                Category = StoredMessageCategory.Assistant,
                CreatedAt = DateTime.UtcNow
            });
            await dbContext.SaveChangesAsync();
            await _chatHubWrapper.SendBlockMessage(messageId, message, new List<AssistantMessageToolCallVo>());
            await _chatHubWrapper.SendInternalLoopTimesReached();
        }
        catch(System.ClientModel.ClientResultException ex){
            var response = ex.GetRawResponse();
            var content = response.Content.ToString();
            
            try{
                var json = JsonNode.Parse(content);
                content = json.ToJsonString(new JsonSerializerOptions{
                    WriteIndented = true,
                    Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
            }catch{}

            _logger.LogError(ex, 
                $"""
                Error in AgentRunner
                Response:
                {content}
                """);

            await _chatHubWrapper.SendDebug($"ClientResultException in AgentRunner: {content}");
            await _chatHubWrapper.SendError("系统开小差了，您可以试试把问题再重复一遍。");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in AgentRunner");
            await _chatHubWrapper.SendDebug($"Exception in AgentRunner: {ex.Message}");
            await _chatHubWrapper.SendError("系统开小差了，您可以试试把问题再重复一遍。");
        }
        finally
        {
            await _chatHubWrapper.SendIdle();
            sessionContextService.SetIdle();
            scope.Dispose();
        }

    }

    private IChatAgent CreateChatAgent(IServiceScope scope, string agentKey)
    {
        var type = _agentKeyTypeAccessor.GetChatAgentType(agentKey);
        if(type is null) throw new Exception("Chat Agent not found");
        
        return (IChatAgent)scope.ServiceProvider.GetRequiredService(type);
    }

}
