using System.Text.Json;
using System.Text.Json.Serialization;
using Aihub.Server.Contexts;
using Aihub.Server.Hubs;
using Aihub.Server.Models.Dto;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SignalR;

namespace Aihub.Server.Services;

public class ChatHubWrapper : IScopedService {
    private readonly IHubContext<ChatHub> _chatHub;
    private readonly ICurrentSessionIdAccessor _currentSessionIdAccessor;
    private readonly IWebHostEnvironment _env;

    public ChatHubWrapper(IHubContext<ChatHub> chatHub, ICurrentSessionIdAccessor currentSessionIdAccessor, IWebHostEnvironment env){
        _chatHub = chatHub;
        _currentSessionIdAccessor = currentSessionIdAccessor;
        _env = env;
    }

    private string GetSessionId(){
        var sessionId = _currentSessionIdAccessor.GetSessionId();
        if(sessionId is null){
            throw new InvalidOperationException("SessionId is not set");
        }
        return sessionId.ToString()!;
    }

    public Task DisconnectClient(string connectionId)
        => _chatHub.Clients.Client(connectionId).SendAsync(
            ChatHubSendBackMessageType.Disconnect.ToString(),
            null,
            CancellationToken.None
        );

    private Task SendMessage(ChatHubSendBackMessageType messageType, object? message) 
        => _chatHub.Clients.Group(GetSessionId()).SendAsync(
            messageType.ToString(),
            (message is not null && message is string) 
                ? message 
                : JsonSerializer.Serialize(message, StandardizedJsonOption.DefaultOptions)
        );

    public Task SendProfile(ProfileVo profile)
        => SendMessage(ChatHubSendBackMessageType.Profile, profile);

    public Task CreateNewSessionSuccess(Guid sessionId)
        => SendMessage(ChatHubSendBackMessageType.CreateNewSessionSuccess, new {
            sessionId
        });

    public Task SendHistoryMessages(IEnumerable<MessageDto> messages)
        => SendMessage(ChatHubSendBackMessageType.HistoryMessages, messages);

    public Task SendBeginStreamingMessage(Guid messageId)
        => SendMessage(ChatHubSendBackMessageType.BeginStreamingMessage, new {
            messageId
        });

    public Task SendStreamingMessage(Guid messageId, string content)
        => SendMessage(ChatHubSendBackMessageType.StreamingMessage, new {
            messageId,
            content
        });

    public Task SendEndStreamingMessage(Guid messageId, string content, List<AssistantMessageToolCallVo> toolCalls)
        => SendMessage(ChatHubSendBackMessageType.EndStreamingMessage, new AssistantMessageVo{
            MessageId = messageId,
            Content = content,
            ToolCalls = toolCalls
        });

    public Task SendBlockMessage(Guid messageId, string content, List<AssistantMessageToolCallVo> toolCalls)
        => SendMessage(ChatHubSendBackMessageType.BlockMessage, new AssistantMessageVo{
            MessageId = messageId,
            Content = content,
            ToolCalls = toolCalls
        });

    public Task SendCanceled()
        => SendMessage(ChatHubSendBackMessageType.Canceled, null);

    public Task SendError(string error)
        => SendMessage(ChatHubSendBackMessageType.Error, error);

    public Task SendBusy()
        => SendMessage(ChatHubSendBackMessageType.Busy, null);
    
    public Task SendIdle()
        => SendMessage(ChatHubSendBackMessageType.Idle, null);

    public Task SendInternalLoopTimesReached()
        => SendMessage(ChatHubSendBackMessageType.InternalLoopTimesReached, null);

    public Task SendDebug(string message)
    {
        if(!_env.IsProduction()){
            return SendMessage(ChatHubSendBackMessageType.Debug, message);
        }
        return Task.CompletedTask;
    }

    public Task SendInformation(string message)
        => SendMessage(ChatHubSendBackMessageType.Information, message);

}

public enum ChatHubSendBackMessageType {
    Profile,
    Busy,
    Idle,
    HistoryMessages,
    StreamingMessage,
    BeginStreamingMessage,
    EndStreamingMessage,
    BlockMessage,
    CreateNewSessionSuccess,
    Disconnect,
    Canceled,
    CriticalError,
    Error,
    InternalLoopTimesReached,
    Debug,
    Information,
}
