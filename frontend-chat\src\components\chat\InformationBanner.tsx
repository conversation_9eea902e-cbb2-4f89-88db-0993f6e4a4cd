import { useState, useEffect } from 'react';
import { Info, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InformationBannerProps {
  message: string;
  isVisible: boolean;
  onClose: () => void;
  autoCloseDelay?: number;
}

export const InformationBanner = ({ 
  message, 
  isVisible, 
  onClose, 
  autoCloseDelay = 7000 
}: InformationBannerProps) => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true);
      
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    } else {
      setIsAnimating(false);
    }
  }, [isVisible, autoCloseDelay, onClose]);

  if (!isVisible && !isAnimating) return null;

  return (
    <div
      className={cn(
        "fixed top-4 left-1/2 transform -translate-x-1/2 z-50",
        "bg-blue-50 border border-blue-200 rounded-lg shadow-lg",
        "px-4 py-3 max-w-md w-full mx-4",
        "transition-all duration-300 ease-in-out",
        isVisible 
          ? "opacity-100 translate-y-0" 
          : "opacity-0 -translate-y-2 pointer-events-none"
      )}
    >
      <div className="flex items-center gap-3">
        <Info className="h-5 w-5 text-blue-600 flex-shrink-0" />
        <p className="text-sm text-blue-800 flex-1">{message}</p>
        <button
          onClick={onClose}
          className="text-blue-600 hover:text-blue-800 transition-colors"
          aria-label="关闭通知"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};