using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites;

/// <summary>
/// 租户实体，代表系统中的一个组织或机构
/// 用于实现多租户架构，支持数据隔离和权限管理
/// </summary>
public class Tenant {
    /// <summary>
    /// 租户的唯一标识符
    /// </summary>
    [Key]
    [Comment("租户的唯一标识符")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 租户的唯一标识符
    /// </summary>
    [Required]
    [Comment("租户的唯一标识符")]
    public required string Key {get;set;}
    
    /// <summary>
    /// 租户名称，用于显示和识别不同的组织
    /// </summary>
    [Comment("租户名称，用于显示和识别不同的组织")]
    public required string Name { get; set; }
    
    /// <summary>
    /// 租户描述信息，提供关于组织的详细说明
    /// </summary>
    [Comment("租户描述信息，提供关于组织的详细说明")]
    public string? Description { get; set; }

    /// <summary>
    /// 租户的密钥，用于验证租户的合法性
    /// </summary>
    [Comment("租户的密钥，用于验证租户的合法性")]
    public string? SecretKey { get; set; }

    /// <summary>
    /// 租户是否禁用
    /// </summary>
    [Comment("租户是否禁用")]
    public bool Disabled { get; set; } = false;
    
    /// <summary>
    /// 租户创建时间，记录组织加入系统的时间
    /// </summary>
    [Comment("租户创建时间，记录组织加入系统的时间")]
    public required DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
