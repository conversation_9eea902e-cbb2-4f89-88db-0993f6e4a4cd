# Production/Default Environment Variables
# SignalR Configuration
VITE_SIGNALR_HUB_URL=/api/chathub
VITE_SIGNALR_RECONNECT_ATTEMPTS=3
VITE_SIGNALR_CONNECTION_TIMEOUT=30000
VITE_SIGNALR_BASE_RETRY_DELAY=1000
VITE_SIGNALR_MAX_RETRY_DELAY=30000
VITE_SIGNALR_EXPONENTIAL_BASE=2
VITE_SIGNALR_MAX_EXPONENTIAL_RETRIES=3

# UI Configuration
VITE_UI_STREAMING_CHAR_DELAY=30
VITE_UI_AUTO_SCROLL_DELAY=100
VITE_UI_TOAST_DURATION=5000

# Message Configuration
VITE_MESSAGE_MAX_CONTENT_LENGTH=8000
VITE_MESSAGE_ID_PREFIX=msg_

# Development Settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info