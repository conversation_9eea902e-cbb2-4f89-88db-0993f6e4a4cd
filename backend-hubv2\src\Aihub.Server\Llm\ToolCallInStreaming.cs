namespace Aihub.Server.Llm;

public class ToolCallInStreaming
{
    /// <summary>
    /// 工具调用ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 函数名称
    /// </summary>
    public string FunctionName { get; set; } = string.Empty;

    /// <summary>
    /// 函数参数（JSON字符串）
    /// </summary>
    public string FunctionArguments { get; set; } = string.Empty;
}