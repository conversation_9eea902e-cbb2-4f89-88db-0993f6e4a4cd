using System.Text.Json.Serialization;

namespace Aihub.Server;


// [JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(UserMessageDto), "UserMessage")]
[JsonDerivedType(typeof(AssistantMessageDto), "AssistantMessage")]
public abstract class MessageDto {
    public required Guid Id {get;set;}
    
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public abstract MessageDtoCategory Category {get;}
    
    public string? Content { get; set; }

    public required DateTime Timestamp { get; set; }
}
