name: 测试环境部署后端(backend-chat)
on:
  workflow_dispatch:
  push:
    branches: [ staging ]
    paths:
      - .github/workflows/backend-chat-staging.yml
      - "backend-hubv2/**"

jobs:
  deploy:
    runs-on: ubuntu-latest

    defaults:
      run:
        shell: bash
        working-directory: backend-hubv2

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build project
      run: dotnet build --configuration Release --no-restore

    - name: Publish project
      run: dotnet publish src/Aihub.Server/Aihub.Server.csproj -c Release -o publish

    - name: Upload build files
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        source: "backend-hubv2/publish/"
        target: "/opt/aihub-staging/upload-backend-chat"

    - name: Finish deployment
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        script: |
          cd /opt/aihub-staging
          rm -rf backend-chat
          cp -r upload-backend-chat/backend-hubv2/publish .
          mv publish backend-chat
          rm -rf upload-backend-chat
          docker compose down backend-chat && docker compose up -d backend-chat
