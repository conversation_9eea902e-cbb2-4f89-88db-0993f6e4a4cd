using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Aihub.Server.Models;
using Aihub.Server.Scrutors;

namespace Aihub.Server.Agent.Tool;

public class ToolsDiscovery : ISingletonService
{

    private Lazy<Dictionary<string, ToolDescription>> _tools = new Lazy<Dictionary<string, ToolDescription>>(() => {
        var tools = new Dictionary<string, ToolDescription>();

        var toolTypes = typeof(ITool).Assembly.GetTypes()
            .Where(t => t.IsPublic) // only public types
            .Where(t => !t.IsAbstract) // only non-abstract types
            .Where(t => !t.IsInterface) // only non-interface types
            .Where(t => typeof(ITool).IsAssignableFrom(t)); // only types that implement ITool

        foreach (var toolType in toolTypes)
        {
            var toolMethods = toolType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                .Where(m => m.GetCustomAttribute<ToolAttribute>() != null);

            foreach (var toolMethod in toolMethods)
            {
                //toolMethod 的返回值必须是 Task<> 的泛型
                if(!(toolMethod.ReturnType.IsGenericType && toolMethod.ReturnType.GetGenericTypeDefinition() == typeof(Task<>))){
                    throw new Exception($"Tool {toolType.Name}.{toolMethod.Name} is not a valid tool");
                }

                var toolInfo = new ToolDescription{
                    ToolId = toolType.Name + "_" + toolMethod.Name,
                    Name = toolMethod.GetCustomAttribute<ToolAttribute>()?.Name ?? toolMethod.Name,
                    Description = toolMethod.GetCustomAttribute<ToolAttribute>()?.Description,

                    NameForUser = toolMethod.GetCustomAttribute<ToolAttribute>()?.NameForUser,
                    DescriptionForUser = toolMethod.GetCustomAttribute<ToolAttribute>()?.DescriptionForUser,

                    Parameters = toolMethod.GetParameters().Select(p => p.Name!).ToArray(),
                    ParameterDescriptions = toolMethod.GetParameters().ToDictionary(p => p.Name!, p => p.GetCustomAttribute<DescriptionAttribute>()?.Description ?? p.Name!),
                    ParameterTypes = toolMethod.GetParameters().ToDictionary(p => p.Name!, p => p.ParameterType.Name switch{
                        "String" => ToolDescriptionParameterType.String,
                        "Int" => ToolDescriptionParameterType.Int,
                        "Float" => ToolDescriptionParameterType.Float,
                        "Boolean" => ToolDescriptionParameterType.Boolean,
                        _ => throw new Exception($"Unsupported parameter type: {p.ParameterType.Name}")
                    }),
                    RequiredParameters = toolMethod.GetParameters().Where(p => p.GetCustomAttribute<RequiredAttribute>() != null).Select(p => p.Name!).ToArray(),
                    ToolType = toolType,
                    ToolMethod = toolMethod,
                    UserAware = toolMethod.GetCustomAttribute<ToolAttribute>()?.UserAware ?? false,
                    NeedConfirm = toolMethod.GetCustomAttribute<ToolAttribute>()?.NeedConfirm ?? false,
                    Severity = toolMethod.GetCustomAttribute<ToolAttribute>()?.Severity ?? ToolSeverity.Low
                };

                if(tools.ContainsKey(toolInfo.ToolId)){
                    throw new Exception($"Tool {toolInfo.ToolId} already exists");
                }

                tools.Add(toolInfo.ToolId, toolInfo);
            }
        }

        return tools;
    });

    public IEnumerable<ToolDescription> DiscoverTools() {
        return _tools.Value.Values;
    }

    public List<ToolDescription> GetAvailableToolDescriptions(params Type[] toolTypes)
    {
        return DiscoverTools()
            .Where(t => toolTypes.Contains(t.ToolType))
            .ToList();
    }
}
