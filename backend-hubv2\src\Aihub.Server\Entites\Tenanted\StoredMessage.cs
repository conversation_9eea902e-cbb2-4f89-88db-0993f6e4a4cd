using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites.Tenanted;

/// <summary>
/// 存储消息实体，用于持久化保存对话中的每一条消息
/// 包含消息的内容、类别、时间戳等信息
/// </summary>
public class StoredMessage {
    /// <summary>
    /// 消息的唯一标识符
    /// </summary>
    [Key]
    [Comment("消息的唯一标识符")]
    public required Guid Id { get; set; }
    
    /// <summary>
    /// 关联的会话ID，标识该消息属于哪个会话（可能是 ChatSession 或 AgentSession)
    /// </summary>
    [Comment("关联的会话ID，标识该消息属于哪个会话（可能是 ChatSession 或 AgentSession)")]
    public required Guid SessionId { get; set; }
    
    
    /// <summary>
    /// 消息类别，标识消息的发送者类型（用户、助手、系统等）
    /// </summary>
    [Comment("消息类别，标识消息的发送者类型（用户、助手、系统等）")]
    public required StoredMessageCategory Category { get; set; }

    /// <summary>
    /// 消息的主要内容，存储实际的对话文本
    /// </summary>
    [Comment("消息的主要内容，存储实际的对话文本")]
    public string? Content { get; set; }
    
    /// <summary>
    /// 额外的消息信息，可用于存储元数据、格式化信息或其他扩展数据
    /// </summary>
    [Comment("额外的消息信息，可用于存储元数据、格式化信息或其他扩展数据")]
    public string? Extra { get; set; }

    /// <summary>
    /// 消息使用的模型
    /// </summary>
    [MaxLength(32)]
    [Comment("消息使用的模型")]
    public string? Model {get;set;}
    
    /// <summary>
    /// 消息创建时间，记录消息发送的精确时间点
    /// </summary>
    [Comment("消息创建时间，记录消息发送的精确时间点")]
    public required DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 输入Token数量
    /// </summary>
    [Comment("输入Token数量")]
    public int InputTokenCount { get; set; }

    /// <summary>
    /// 输出Token数量
    /// </summary>
    [Comment("输出Token数量")]
    public int OutputTokenCount { get; set; }

}


/// <summary>
/// 定义消息类别的枚举，用于标识AI聊天系统中不同类型的消息发送者
/// </summary>
public enum StoredMessageCategory {
    /// <summary>
    /// 用户消息：由人类用户发送的消息
    /// </summary>
    User,
    
    /// <summary>
    /// 助手消息：由AI助手发送的回复消息
    /// </summary>
    Assistant,
    
    /// <summary>
    /// 系统消息：由系统自动生成的提示或通知消息
    /// </summary>
    System,
    
    /// <summary>
    /// 工具消息：工具调用或工具返回的结果消息
    /// </summary>
    Tool,

    /// <summary>
    /// 子代理消息：子代理工具调用或工具返回的结果消息
    /// </summary>
    SubAgentAssistant,

    /// <summary>
    /// 标记（仅用于内部使用）
    /// </summary>
    Internal,
}
