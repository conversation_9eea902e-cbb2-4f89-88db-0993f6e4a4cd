using System.ClientModel;
using Aihub.Server.Agent;
using Aihub.Server.EfCore;
using Aihub.Server.Filters;
using Aihub.Server.Hubs;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using OpenAI;
using OpenAI.Chat;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// 添加 Entity Framework Core 和 SQLite 支持
builder.Services.AddDbContextFactory<AihubDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection") ?? "Host=localhost;Database=aihub;Username=postgres;Password=****"));

// 添加 CORS 服务
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.SetIsOriginAllowed(origin => true)
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// 添加 SignalR 服务
builder.Services.AddSignalR();

// 添加 Scrutor 服务
// look https://github.com/khellang/Scrutor
builder.Services.Scan(scan => scan
    .FromAssemblyOf<Program>()
        .AddClasses(classes => classes.AssignableTo<ITransientService>())
            .AsImplementedInterfaces()
            .AsSelf()
            .WithTransientLifetime()
        .AddClasses(classes => classes.AssignableTo<IScopedService>())
            .AsSelfWithInterfaces()
            .WithScopedLifetime()
        .AddClasses(classes => classes.AssignableTo<ISingletonService>())
            .AsSelfWithInterfaces()
            .WithSingletonLifetime());

// 添加 OpenAI 服务
builder.Services.AddTransient<ChatClient>(sp => {
    var aiHubOption = sp.GetRequiredService<IOptions<AihubOption>>();
    var chatClient = new ChatClient(
        model: aiHubOption.Value.DefaultModel,
        credential: new ApiKeyCredential(aiHubOption.Value.OpenRouterApiKey), 
        options: new OpenAIClientOptions{
            Endpoint =  new Uri("https://openrouter.ai/api/v1")
            //Endpoint = new Uri("http://127.0.0.1:8080")
        }
    );
    return chatClient;
});

builder.Services.Configure<AihubOption>(builder.Configuration.GetSection("Aihub"));

// 添加控制器支持
builder.Services.AddControllers(options =>
{
    options.Filters.Add<ApiResponseWrapper>();
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    using(var scope = app.Services.CreateScope())
    {
        var dbContext = scope.ServiceProvider.GetRequiredService<AihubDbContext>();
        dbContext.Database.EnsureDeleted();
        dbContext.Database.EnsureCreated();
        var dataSeeder = scope.ServiceProvider.GetRequiredService<DataSeeder>();
        await dataSeeder.SeedAsync();
    }
    app.MapOpenApi();
}

// 启用 CORS
app.UseCors("AllowAll");

// 添加静态文件支持
app.UseStaticFiles();

// 添加控制器路由
app.MapControllers();

// 映射 SignalR Hub
app.MapHub<ChatHub>("/api/chathub");

// Fallback to index.html for any unmatched routes
app.MapFallbackToFile("index.html");

app.Run();
