import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Bell, Search, LogOut, Clock, CheckCircle, AlertCircle, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

// 模拟通知数据
const notifications = [
  {
    id: 1,
    title: "系统维护通知",
    message: "系统将于今晚22:00-24:00进行维护升级",
    fullContent: "尊敬的用户，为了提供更好的服务体验，我们将于今晚22:00-24:00对系统进行维护升级。维护期间可能会影响部分功能的正常使用，请您提前做好相关准备。如有紧急情况，请联系技术支持。感谢您的理解与配合。",
    time: "2分钟前",
    timestamp: "2024-01-15 14:30",
    type: "info",
    read: false,
  },
  {
    id: 2,
    title: "API调用异常",
    message: "客服机器人应用出现高错误率，请及时检查",
    fullContent: "监控系统检测到客服机器人应用在过去1小时内出现异常高的错误率（错误率：35%）。主要错误类型包括：API超时、服务不可用、参数错误。建议立即检查应用配置、网络连接状态，并查看详细错误日志以确定根本原因。",
    time: "10分钟前",
    timestamp: "2024-01-15 14:22",
    type: "error",
    read: false,
  },
  {
    id: 3,
    title: "新用户注册",
    message: "用户 <EMAIL> 已成功注册",
    fullContent: "新用户注册详情：\n邮箱：<EMAIL>\n注册时间：2024-01-15 13:30\n注册来源：官网\n用户角色：普通用户\n初始权限：基础权限组\n\n该用户已通过邮箱验证，账户状态为活跃。",
    time: "1小时前",
    timestamp: "2024-01-15 13:30",
    type: "success",
    read: true,
  },
  {
    id: 4,
    title: "存储空间提醒",
    message: "当前存储使用率已达80%，建议清理历史数据",
    fullContent: "存储空间使用情况：\n总容量：5GB\n已使用：4GB (80%)\n剩余容量：1GB\n\n建议操作：\n1. 清理30天前的会话记录\n2. 压缩或删除不必要的文件\n3. 考虑升级存储套餐\n\n如需协助，请联系技术支持团队。",
    time: "2小时前",
    timestamp: "2024-01-15 12:30",
    type: "warning",
    read: true,
  },
  {
    id: 5,
    title: "智能体更新",
    message: "销售助手智能体已更新至v2.1版本",
    fullContent: "销售助手智能体v2.1更新内容：\n\n新功能：\n- 增强的自然语言理解能力\n- 新增产品推荐算法\n- 支持多轮对话上下文保持\n\n优化改进：\n- 响应速度提升30%\n- 减少误解率15%\n- 优化内存使用\n\n该更新已自动应用，无需手动操作。",
    time: "昨天",
    timestamp: "2024-01-14 15:45",
    type: "info",
    read: true,
  },
];

export function Header() {
  const [selectedNotification, setSelectedNotification] = useState<typeof notifications[0] | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const handleNotificationClick = (notification: typeof notifications[0]) => {
    setSelectedNotification(notification);
    setIsDialogOpen(true);
    
    // 标记为已读
    if (!notification.read) {
      toast({
        title: "通知已标记为已读",
        duration: 2000,
      });
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Bell className="h-4 w-4 text-blue-500" />;
    }
  };
  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <div className="flex items-center space-x-2">
              {selectedNotification && getNotificationIcon(selectedNotification.type)}
              <DialogTitle>{selectedNotification?.title}</DialogTitle>
            </div>
            <DialogDescription className="text-sm text-muted-foreground">
              发送时间: {selectedNotification?.timestamp}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="p-4 bg-muted/30 rounded-lg">
              <pre className="whitespace-pre-wrap text-sm text-foreground font-sans">
                {selectedNotification?.fullContent}
              </pre>
            </div>
            <div className="flex justify-end">
              <Button onClick={() => setIsDialogOpen(false)}>
                关闭
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

    <header className="h-16 bg-card border-b border-border px-6 flex items-center justify-between">
      {/* Search */}
      <div className="flex items-center flex-1 max-w-md">
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="搜索应用、智能体..."
            className="pl-10 bg-muted border-0 focus-visible:ring-1"
          />
        </div>
      </div>

      {/* Right side */}
      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="h-4 w-4" />
              {notifications.filter(n => !n.read).length > 0 && (
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-xs flex items-center justify-center text-white">
                  {notifications.filter(n => !n.read).length}
                </span>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80" align="end">
            <DropdownMenuLabel className="flex items-center justify-between">
              <span>通知</span>
              <Badge variant="secondary" className="text-xs">
                {notifications.filter(n => !n.read).length} 条未读
              </Badge>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <ScrollArea className="h-80">
              <div className="space-y-1">
                {notifications.map((notification) => (
                  <DropdownMenuItem
                    key={notification.id}
                    className={`flex flex-col items-start space-y-1 p-3 cursor-pointer hover:bg-muted/70 ${
                      !notification.read ? 'bg-muted/50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start justify-between w-full">
                      <div className="flex items-start space-x-2 flex-1">
                        <div className="mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-foreground truncate">
                            {notification.title}
                          </p>
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {notification.message}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end ml-2">
                        {!notification.read && (
                          <div className="w-2 h-2 bg-primary rounded-full mb-1"></div>
                        )}
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Clock className="h-3 w-3 mr-1" />
                          {notification.time}
                        </div>
                      </div>
                    </div>
                  </DropdownMenuItem>
                ))}
              </div>
            </ScrollArea>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-center justify-center text-primary">
              查看全部通知
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-8 w-8 rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder.svg" alt="用户头像" />
                <AvatarFallback>管理</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">租户管理员</p>
                <p className="text-xs leading-none text-muted-foreground">
                  <EMAIL>
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <LogOut className="mr-2 h-4 w-4" />
              <span>退出登录</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
    </>
  );
}