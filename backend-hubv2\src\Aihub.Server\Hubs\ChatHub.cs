using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;
using Aihub.Server.EfCore;
using Aihub.Server.Models;
using Aihub.Server.Contexts;
using Aihub.Server.Services;
using Aihub.Server.Converters;

namespace Aihub.Server.Hubs;

public class ChatHub : Hub
{

    private readonly ILogger<ChatHub> _logger;
    private readonly SessionContextService _sessionContextService;

    public ChatHub(
        ILogger<ChatHub> logger,
        SessionContextService sessionContextService
        ){
        _logger = logger;
        _sessionContextService = sessionContextService;
    }


    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("用户 {UserIdentifier} 连接，ConnectionId: {ConnectionId}", Context.UserIdentifier, Context.ConnectionId);
        await base.OnConnectedAsync();
        try
        {
            await _sessionContextService.OnConnectedAsync(Context.ConnectionId, Context.UserIdentifier!,
                async (sessionId) => await Groups.AddToGroupAsync(Context.ConnectionId, sessionId));

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接建立失败，用户: {UserIdentifier}，Exception: {Exception}", Context.UserIdentifier, ex);
            await HandleException(ex);
            throw;
        }
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("用户 {UserIdentifier} 断开连接，Exception: {Exception}", Context.UserIdentifier, exception);

        if(exception is not null) await HandleException(exception);

        await base.OnDisconnectedAsync(exception);
    }

    private async Task HandleException(Exception exception){
        var userFriendlyMessage = await _sessionContextService.HandleExcpetionWithUserFriendlyMessage(exception);
        await Clients.Caller.SendAsync("CriticalError", new { content = userFriendlyMessage });
    }


    public async Task CreateNewSession()
    {  
        _logger.LogInformation("用户 {UserIdentifier} 创建新会话，ConnectionId: {ConnectionId}", Context.UserIdentifier, Context.ConnectionId);

        var latestSessionId = await _sessionContextService.GetLatestSessionId(Context.UserIdentifier!);
        if(latestSessionId is not null){
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, latestSessionId);
        }

        var sessionId = await _sessionContextService.CreateNewSession(Context.UserIdentifier!);
        await Groups.AddToGroupAsync(Context.ConnectionId, sessionId);
        await Clients.Caller.SendAsync("Debug",$"CreateNewSession SessionId: {sessionId}");
    }

    public async Task Cancel(){
        _logger.LogInformation("用户 {UserIdentifier} 取消会话，ConnectionId: {ConnectionId}", Context.UserIdentifier, Context.ConnectionId);
        await _sessionContextService.Cancel(Context.UserIdentifier);
    }

    public async Task SendMessage(string message)
    {
        _logger.LogInformation("用户 {UserIdentifier} 发送消息，ConnectionId: {ConnectionId}, Message: {Message}", Context.UserIdentifier, Context.ConnectionId, message);
        await _sessionContextService.UserMessage(Context.UserIdentifier, message);
    }

    // public async Task ConfirmToolCall(Guid messageId, Guid toolId, bool isConfirm){

    //     // InitCurrentClientIdentity();
    //     // var sessionContext = await ResumeOrCreateSessionContext();
    //     // sessionContext.CancelIfNotIdle();

    //     // await _chatHubWrapper.SendBusy();
    //     // var isAllToolCallsConfirmed = await sessionContext.SetToolCallConfirmReturnTrueIfAllConfirmedAsync(messageId, toolId, isConfirm, CancellationToken.None);
    //     // if(isAllToolCallsConfirmed){
    //     //     await sessionContext.KickStartAsync(CancellationToken.None);
    //     // }else{
    //     //     await _chatHubWrapper.SendIdle();
    //     // }

    // }
} 