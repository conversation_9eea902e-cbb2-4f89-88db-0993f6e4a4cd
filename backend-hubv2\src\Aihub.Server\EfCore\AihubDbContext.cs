using Microsoft.EntityFrameworkCore;
using Aihub.Server.Entites;
using Aihub.Server.Entites.Tenanted;

namespace Aihub.Server.EfCore;

public class AihubDbContext : DbContext
{
    public AihubDbContext(DbContextOptions<AihubDbContext> options) : base(options)
    {
    }

    #region Tenanted
    public DbSet<Application> Applications { get; set; }
    public DbSet<AgentOption> AgentOptions { get; set; }
    public DbSet<Session> Sessions { get; set; }
    public DbSet<ClientIdentity> ClientIdentities { get; set; }
    public DbSet<TenantUser> TenantUsers { get; set; }
    public DbSet<StoredMessage> StoredMessages { get; set; }
    #endregion

    #region Common
    public DbSet<Ticket> Tickets { get; set; }
    public DbSet<Menu> Menus { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<RoleMenu> RoleMenus { get; set; }
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    #endregion

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder.Properties<Enum>()
            .HaveConversion<string>();
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 显式配置所有枚举属性的字符串转换
        modelBuilder.Entity<StoredMessage>()
            .Property(e => e.Category)
            .HasConversion<string>();

        modelBuilder.Entity<Session>()
            .Property(e => e.Type)
            .HasConversion<string>();

        modelBuilder.Entity<User>()
            .Property(e => e.Status)
            .HasConversion<string>();

        modelBuilder.Entity<Menu>()
            .Property(e => e.Type)
            .HasConversion<string>();

        modelBuilder.Entity<Role>()
            .Property(e => e.Status)
            .HasConversion<string>();

        modelBuilder.Entity<Role>()
            .Property(e => e.DataScope)
            .HasConversion<string>();

        modelBuilder.Entity<Ticket>()
            .Property(e => e.TicketType)
            .HasConversion<string>();
    }
}