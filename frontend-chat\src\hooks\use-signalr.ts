import { useState, useEffect, useRef, useCallback } from 'react';
import { HubConnectionBuilder, HubConnection, HubConnectionState } from '@microsoft/signalr';
import { APP_CONFIG } from '@/config/constants';
import { generateMessageId, generateClientIdentityId } from '@/lib/message-utils';
import { errorHandler } from '@/lib/error-handler';
import type {
  MessageDto,
  StreamingMessage,
  BeginStreamingMessage,
  EndStreamingMessage,
  BlockMessage,
  CreateNewSessionSuccess,
  Message,
  UseSignalROptions,
  UseSignalRReturn,
  Profile,
} from '@/types/chat';
import { toast } from './use-toast';
import { useTheme, type Theme } from './use-theme';

// Re-export types for convenience
export type { ToolCallDto, Message } from '@/types/chat';


/**
 * Custom hook for managing SignalR connection to AI hub
 * Provides real-time messaging, connection management, and tool call handling
 * 
 * @param options - Configuration options for the SignalR connection
 * @returns Object containing connection state, messages, and control methods
 */
export const useSignalR = (options: UseSignalROptions = {}): UseSignalRReturn => {
  const {
    url = APP_CONFIG.SIGNALR.DEFAULT_URL,
    ticket = APP_CONFIG.SIGNALR.DEFAULT_TICKET,
    onConnectionStateChange,
    onCriticalError
  } = options;

  const [connection, setConnection] = useState<HubConnection | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [profile, setProfile] = useState<Profile | null>(APP_CONFIG.PROFILE.DEFAULT);
  const [isLoading, setIsLoading] = useState(false);
  const [informationBanner, setInformationBanner] = useState<{
    isVisible: boolean;
    message: string;
  }>({ isVisible: false, message: '' });
  const connectionRef = useRef<HubConnection | null>(null);
  const { setTheme } = useTheme();
  const setThemeRef = useRef(setTheme);
  
  // Update ref when setTheme changes
  useEffect(() => {
    setThemeRef.current = setTheme;
  }, [setTheme]);

  /**
   * Event handlers for SignalR messages
   * Each handler processes a specific type of message from the server
   */
  const handleBeginStreamingMessage = useCallback((beginStreamingData: string) => {
    const beginStreamingMessage: BeginStreamingMessage = JSON.parse(beginStreamingData);
    setMessages(prev => [...prev, {
      id: beginStreamingMessage.messageId,
      content: "",
      category: 'Assistant',
      timestamp: new Date(),
      isFinished: false,
      isStreaming: true
    }]);
  }, []);

  const handleStreamingMessage = useCallback((streamingData: string) => {
    const streamingMessage: StreamingMessage = JSON.parse(streamingData);
    setMessages(prev => {
      const messageIndex = prev.findIndex(msg => msg.id === streamingMessage.messageId);
      if (messageIndex !== -1) {
        const updatedMessages = [...prev];
        updatedMessages[messageIndex] = {
          ...updatedMessages[messageIndex],
          content: updatedMessages[messageIndex].content + streamingMessage.content
        };
        return updatedMessages;
      }
      return prev;
    });
  }, []);

  const handleEndStreamingMessage = useCallback((endStreamingData: string) => {
    const endStreamingMessage: EndStreamingMessage = JSON.parse(endStreamingData);
    setMessages(prev => prev.map(msg => 
      msg.id === endStreamingMessage.messageId 
        ? {
            ...msg,
            content: endStreamingMessage.content,
            isFinished: true,
            isStreaming: false,
            toolCalls: endStreamingMessage.toolCalls
          }
        : msg
    ));
  }, []);

  const handleHistoryMessages = useCallback((historyData: string) => {
    const historyMessages: MessageDto[] = JSON.parse(historyData);
    const convertedMessages: Message[] = historyMessages.map(msg => ({
      id: msg.id,
      content: msg.content,
      category: msg.category,
      timestamp: new Date(msg.timestamp),
      isFinished: true,
      toolCalls: msg.toolCalls
    }));
    setMessages(convertedMessages);
  }, []);

  const handleCreateNewSessionSuccess = useCallback((createNewSessionSuccessData: string) => {
    const createNewSessionSuccess: CreateNewSessionSuccess = JSON.parse(createNewSessionSuccessData);
    // Session created successfully, no user-facing action needed
  }, []);

  const handleBlockMessage = useCallback((blockMessageData: string) => {
    const blockMessage: BlockMessage = JSON.parse(blockMessageData);
    setMessages(prev => [...prev, {
      id: blockMessage.messageId,
      content: blockMessage.content,
      category: 'Assistant',
      timestamp: new Date(),
      isFinished: true,
      toolCalls: blockMessage.toolCalls
    }]);
  }, []);

  const handleProfile = useCallback((profileData: string) => {
    const profile: Profile = JSON.parse(profileData);
    profile.fetched = true;
    setProfile(profile);
    
    // Set theme based on profile theme if specified
    if (profile.theme && (profile.theme === 'light' || profile.theme === 'dark')) {
      setThemeRef.current(profile.theme as Theme);
    }
    
    toast({
      title: "已连接",
      description: "已经成功连接服务器。",
    });
  }, []);

  const handleBusy = useCallback(() => {
    setIsLoading(true);
  }, []);

  const handleIdle = useCallback(() => {
    setIsLoading(false);
  }, []);

  const handleError = useCallback((errorData: string) => {
    setMessages(prev => [...prev, {
      id: generateMessageId(),
      content: errorData,
      category: 'Error',
      timestamp: new Date(),
      isFinished: true
    }]);
    setIsLoading(false);
  }, []);

  const handleCriticalError = useCallback((errorData: { content: string }) => {
    connectionRef.current?.stop();
    onCriticalError?.(errorData.content);
  }, [onCriticalError]);

  const handleInternalLoopTimesReached = useCallback(() => {

    //nothing happen

  }, []);

  const handleDebug = useCallback((debugData: string) => {
    console.log('Remote-Debug:', debugData);
  }, []);

  const handleInformation = useCallback((informationData: string) => {
    console.log('Remote-Information:', informationData);
    setInformationBanner({
      isVisible: true,
      message: informationData
    });
  }, []);


  /**
   * Register all SignalR event handlers for the connection
   * @param connection - The SignalR connection instance
   */
  const registerEventHandlers = useCallback((connection: HubConnection) => {
    connection.on('CreateNewSessionSuccess', handleCreateNewSessionSuccess);
    connection.on('BeginStreamingMessage', handleBeginStreamingMessage);
    connection.on('StreamingMessage', handleStreamingMessage);
    connection.on('EndStreamingMessage', handleEndStreamingMessage);
    connection.on('BlockMessage', handleBlockMessage);
    connection.on('HistoryMessages', handleHistoryMessages);
    connection.on('Profile', handleProfile);
    connection.on('Busy', handleBusy);
    connection.on('Idle', handleIdle);
    connection.on('Error', handleError);
    connection.on('CriticalError', handleCriticalError);
    connection.on('InternalLoopTimesReached', handleInternalLoopTimesReached);
    connection.on('Debug', handleDebug);
    connection.on('Information', handleInformation);
  }, [
    handleCreateNewSessionSuccess,
    handleBeginStreamingMessage,
    handleStreamingMessage,
    handleEndStreamingMessage,
    handleBlockMessage,
    handleHistoryMessages,
    handleProfile,
    handleBusy,
    handleIdle,
    handleError,
    handleCriticalError,
    handleInternalLoopTimesReached,
    handleDebug,
    handleInformation
  ]);

  /**
   * Unregister all SignalR event handlers from the connection
   * @param connection - The SignalR connection instance
   */
  const unregisterEventHandlers = useCallback((connection: HubConnection) => {
    connection.off('CreateNewSessionSuccess', handleCreateNewSessionSuccess);
    connection.off('BeginStreamingMessage', handleBeginStreamingMessage);
    connection.off('StreamingMessage', handleStreamingMessage);
    connection.off('EndStreamingMessage', handleEndStreamingMessage);
    connection.off('BlockMessage', handleBlockMessage);
    connection.off('HistoryMessages', handleHistoryMessages);
    connection.off('Profile', handleProfile);
    connection.off('Busy', handleBusy);
    connection.off('Idle', handleIdle);
    connection.off('Error', handleError);
    connection.off('CriticalError', handleCriticalError);
    connection.off('InternalLoopTimesReached', handleInternalLoopTimesReached);
    connection.off('Debug', handleDebug);
    connection.off('Information', handleInformation);
  }, [
    handleCreateNewSessionSuccess,
    handleBeginStreamingMessage,
    handleStreamingMessage,
    handleEndStreamingMessage,
    handleBlockMessage,
    handleHistoryMessages,
    handleProfile,
    handleBusy,
    handleIdle,
    handleError,
    handleCriticalError,
    handleInternalLoopTimesReached,
     handleDebug,
    handleInformation
  ]);

  // Initialize connection
  useEffect(() => {
    // Don't create connection if ticket is null (handled at page level)
    if (ticket === null) {
      return;
    }

    const newConnection = new HubConnectionBuilder()
      .withUrl(`${url}?ticket=${ticket}`)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: retryContext => {
          // Exponential backoff: 2s, 4s, 8s, then 30s
          if (retryContext.previousRetryCount < 3) {
            return Math.pow(2, retryContext.previousRetryCount + 1) * 1000;
          }
          return 30000;
        }
      })
      .build();

    connectionRef.current = newConnection;
    setConnection(newConnection);

    return () => {
      // Ensure proper cleanup
      const currentConnection = connectionRef.current;
      if (currentConnection) {
        // Remove all event listeners before stopping
        if (currentConnection.state !== 'Disconnected') {
          unregisterEventHandlers(currentConnection);
          currentConnection.stop().catch(error => {
            // Log cleanup errors but don't throw
            if (process.env.NODE_ENV === 'development') {
              console.warn('Error during connection cleanup:', error);
            }
          });
        }
        connectionRef.current = null;
      }
    };
  }, [url, ticket, unregisterEventHandlers]);

  // Setup connection and event handlers
  useEffect(() => {
    if (connection) {
      // Check if connection is already started or starting
      if (connection.state === HubConnectionState.Disconnected) {
        connection.start()
          .then(() => {
            onConnectionStateChange?.(connection.state);
            registerEventHandlers(connection);
          })
          .catch(error => {
            onConnectionStateChange?.(connection.state);
            errorHandler.handleError({
              message: error.message || 'Unknown connection error',
              context: 'SignalR Connection',
              error,
              severity: 'high'
            }, onCriticalError);
          });
      } else if (connection.state === HubConnectionState.Connected) {
        // If already connected, just set up event handlers
        onConnectionStateChange?.(connection.state);
        registerEventHandlers(connection);
      }

      // Handle connection state changes with cleanup
      connection.onclose((error) => {
        setIsLoading(false);
        onConnectionStateChange?.(connection.state);
        if (error && process.env.NODE_ENV === 'development') {
          console.warn('Connection closed with error:', error);
        }
      });

      connection.onreconnecting((error) => {
        setIsLoading(false);
        onConnectionStateChange?.(connection.state);
        if (error && process.env.NODE_ENV === 'development') {
          console.info('Reconnecting due to error:', error);
        }
      });

      connection.onreconnected((connectionId) => {
        onConnectionStateChange?.(connection.state);
        if (process.env.NODE_ENV === 'development') {
          console.info('Reconnected with ID:', connectionId);
        }
      });
    }

    return () => {
      if (connection) {
        unregisterEventHandlers(connection);
      }
    };
  }, [
    connection,
    registerEventHandlers,
    unregisterEventHandlers,
    onConnectionStateChange,
    onCriticalError
  ]);

  /**
   * Public API methods
   */
  /**
   * Send a message to the AI assistant
   * @param message - The message content to send
   */
  const sendMessage = useCallback(async (message: string) => {
    if (!connection || connection.state !== HubConnectionState.Connected) {
      throw new Error('Not connected to server');
    }

    const userMessage: Message = {
      id: generateMessageId(),
      content: message,
      category: 'User',
      timestamp: new Date(),
      isFinished: true
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      await connection.invoke('SendMessage', message);
    } catch (error) {
      setIsLoading(false);
      errorHandler.handleError({
        message: (error as Error).message || 'Unknown error',
        context: 'Message Send',
        error: error as Error,
        severity: 'medium'
      }, onCriticalError);
      throw error;
    }
  }, [connection, onCriticalError]);

  /**
   * Create a new chat session, clearing current messages
   */
  const createNewSession = useCallback(async () => {
    if (!connection || connection.state !== HubConnectionState.Connected) {
      throw new Error('Not connected to server');
    }

    try {
      await connection.invoke('CreateNewSession');
      setMessages([]);
    } catch (error) {
      errorHandler.handleError({
        message: (error as Error).message || 'Unknown error',
        context: 'New Session',
        error: error as Error,
        severity: 'medium'
      }, onCriticalError);
      throw error;
    }
  }, [connection, onCriticalError]);

  /**
   * Confirm or reject a tool call from the AI assistant
   * @param messageId - ID of the message containing the tool call
   * @param toolCallId - ID of the specific tool call
   * @param isConfirm - Whether to confirm (true) or reject (false) the tool call
   */
  const confirmToolCall = useCallback(async (messageId: string, toolCallId: string, isConfirm: boolean) => {
    if (!connection || connection.state !== HubConnectionState.Connected) {
      throw new Error('Not connected to server');
    }

    try {
      await connection.invoke('ConfirmToolCall', messageId, toolCallId, isConfirm);
      
      // Update local state to reflect the confirmation
      setMessages(prev => prev.map(msg => {
        if (msg.id === messageId && msg.toolCalls) {
          return {
            ...msg,
            toolCalls: msg.toolCalls.map(tool => 
              tool.id === toolCallId 
                ? { ...tool, isConfirm: isConfirm }
                : tool
            )
          };
        }
        return msg;
      }));
    } catch (error) {
      errorHandler.handleError({
        message: (error as Error).message || 'Unknown error',
        context: 'Tool Confirmation',
        error: error as Error,
        severity: 'medium'
      }, onCriticalError);
      throw error;
    }
  }, [connection, onCriticalError]);

  /**
   * Clear all messages from the local state
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  /**
   * Close the information banner
   */
  const closeInformationBanner = useCallback(() => {
    setInformationBanner(prev => ({ ...prev, isVisible: false }));
  }, []);


  const result = {
    connection,
    messages,
    profile,
    isLoading,
    informationBanner,
    sendMessage,
    createNewSession,
    confirmToolCall,
    clearMessages,
    closeInformationBanner
  };

  // Add debug setters only in development mode
  if (process.env.NODE_ENV === 'development') {
    (result as UseSignalRReturn & { _debugSetters?: unknown })._debugSetters = {
      setInformationBanner,
      setMessages,
      setIsLoading,
      handleCriticalError
    };
  }

  return result;
};