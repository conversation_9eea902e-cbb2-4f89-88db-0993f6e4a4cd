namespace Aihub.Server.Models.Dto;

/// <summary>
/// 用户信息
/// </summary>
public class ProfileVo {
    
    /// <summary>
    /// 应用程序标识符，用于标识不同的业务应用
    /// </summary>
    public required string Key { get; set; }

    /// <summary>
    /// 应用程序显示名称
    /// </summary>
    public string? DisplayName { get; set; }
    
    /// <summary>
    /// 应用程序描述信息，说明应用的用途和功能
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 应用程序图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 应用程序退出URL
    /// </summary>
    public string? SignOutUrl { get; set; }

    /// <summary>
    /// 应用程序退出URL
    /// </summary>
    public string? Theme { get; set; }

    /// <summary>
    /// 应用程序AI头像
    /// </summary>
    public string? AiAvatar { get; set; }

    /// <summary>
    /// 应用程序用户头像
    /// </summary>
    public string? UserAvatar { get; set; }

    /// <summary>
    /// 推荐开始提示词
    /// </summary>
    public List<string> RecommendedStartPrompts { get; set; } = new();

}
