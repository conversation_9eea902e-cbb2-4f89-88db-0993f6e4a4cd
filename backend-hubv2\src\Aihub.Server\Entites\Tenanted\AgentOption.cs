using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites.Tenanted;

/// <summary>
/// 智能体实体，用于存储智能体信息
/// </summary>
public class AgentOption {

    /// <summary>
    /// 智能体ID
    /// </summary>
    [Key]
    [Comment("智能体ID")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 父级智能体ID，用于标识该智能体属于哪个智能体
    /// </summary>
    [Comment("父级智能体ID，用于标识该智能体属于哪个智能体")]
    public Guid? ParentId { get; set; }

    /// <summary>
    /// 智能体名称
    /// </summary>
    [Comment("智能体名称")]
    public required string Name { get; set; }

    /// <summary>
    /// 智能体描述
    /// </summary>
    [Comment("智能体描述")]
    public required string Description { get; set; }

    /// <summary>
    /// 智能体Key
    /// </summary>
    [Comment("智能体Key")]
    public required string Key { get; set; }

    /// <summary>
    /// 智能体额外信息
    /// </summary>
    [Comment("智能体额外信息")]
    public string? Extra { get; set; }

    /// <summary>
    /// 智能体创建时间，记录智能体注册到系统的时间
    /// </summary>
    [Comment("智能体创建时间，记录智能体注册到系统的时间")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 智能体更新时间，记录智能体更新到系统的时间
    /// </summary>
    [Comment("智能体更新时间，记录智能体更新到系统的时间")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 智能体是否归档，记录智能体是否被归档
    /// </summary>
    [Comment("智能体是否归档，记录智能体是否被归档")]
    public bool IsArchived { get; set; } = false;

    /// <summary>
    /// 子智能体，用于标识该智能体有哪些子智能体
    /// </summary>
    [ForeignKey(nameof(ParentId))]
    [Comment("子智能体，用于标识该智能体有哪些子智能体")]
    public ICollection<AgentOption> Children { get; set; } = new List<AgentOption>();

}


public enum AgentOptionDatabaseAgentExtraDbType {
    MySql,
    PostgreSql,
}
public class AgentOptionDatabaseAgentExtra {
    [Comment("数据库类型")]
    public AgentOptionDatabaseAgentExtraDbType DbType { get; set; }
    [Comment("数据库连接字符串")]
    public string? DbConnectionString { get; set; }
    [Comment("缓存的数据库架构")]
    public string? CachedSchema { get; set; }
    [Comment("缓存时间")]
    public DateTime? CachedAt { get; set; }
}

public class AgentOptionManualGuideAgentExtra {
    [Comment("手动指导手册")]
    public string? ManualGuide { get; set; }
}
