using System.Text.Json;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Models;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;

namespace Aihub.Server.Converters;

public class StoredToMessageConverter : ISingletonService {

    public static Message ConvertToMessage(StoredMessage storedMessage){

        dynamic? extra = string.IsNullOrWhiteSpace(storedMessage.Extra) ? null : JsonSerializer.Deserialize<dynamic>(storedMessage.Extra);

        switch(storedMessage.Category){
            case StoredMessageCategory.System:
                return new SystemMessage{
                    Id = storedMessage.Id,
                    Session = storedMessage.SessionId,
                    Content = storedMessage.Content,
                    CreatedAt = storedMessage.CreatedAt,
                };
            case StoredMessageCategory.Assistant:
                return new AssistantMessage{
                    Id = storedMessage.Id,
                    Session = storedMessage.SessionId,
                    Content = storedMessage.Content,
                    CreatedAt = storedMessage.CreatedAt,
                    Extra = extra is null ? null : JsonSerializer.Deserialize<AssistantMessageExtra>(extra, StandardizedJsonOption.DefaultOptions),
                };
            case StoredMessageCategory.SubAgentAssistant:
                return new SubAgentAssistantMessage{
                    Id = storedMessage.Id,
                    Session = storedMessage.SessionId,
                    Content = storedMessage.Content,
                    CreatedAt = storedMessage.CreatedAt,
                    Extra = extra is null ? null : JsonSerializer.Deserialize<AssistantMessageExtra>(extra, StandardizedJsonOption.DefaultOptions),
                };
            case StoredMessageCategory.User:
                return new UserMessage{
                    Id = storedMessage.Id,
                    Session = storedMessage.SessionId,
                    Content = storedMessage.Content,
                    CreatedAt = storedMessage.CreatedAt,
                };
            case StoredMessageCategory.Tool:
                return new ToolMessage{
                    Id = storedMessage.Id,
                    Session = storedMessage.SessionId,
                    Content = storedMessage.Content,
                    CreatedAt = storedMessage.CreatedAt,
                    Extra = extra is null ? null : JsonSerializer.Deserialize<ToolMessageExtra>(extra, StandardizedJsonOption.DefaultOptions),
                };

            default:
                throw new InvalidOperationException("Invalid message type");
        }
    }
}
