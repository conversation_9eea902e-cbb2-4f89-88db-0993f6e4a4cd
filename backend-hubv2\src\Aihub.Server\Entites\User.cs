using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites;

/// <summary>
/// 用户实体，用于存储用户信息
/// </summary>
public class User {
    /// <summary>
    /// 用户ID
    /// </summary>
    [Key]
    [Comment("用户ID")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    [Comment("用户名")]
    public required string Username { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    [Comment("昵称")]
    public string? Nickname { get; set; }

    /// <summary>
    /// 性别（1-男 2-女 0-保密）
    /// </summary>
    [Comment("性别（1-男 2-女 0-保密）")]
    public byte? Gender { get; set; } = 1;

    /// <summary>
    /// 密码
    /// </summary>
    [Comment("密码")]
    public string? Password { get; set; }

    /// <summary>
    /// 状态(1-正常 0-禁用)
    /// </summary>
    [Comment("状态(1-正常 0-禁用)")]
    public UserStatus Status { get; set; } = UserStatus.Active;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Comment("创建时间")]
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    [Comment("创建人ID")]
    public Guid? CreateBy { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Comment("更新时间")]
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 修改人ID
    /// </summary>
    [Comment("修改人ID")]
    public Guid? UpdateBy { get; set; }

    /// <summary>
    /// 是否归档(false-未归档 true-已归档)
    /// </summary>
    [Comment("是否归档(false-未归档 true-已归档)")]
    public bool IsArchived { get; set; } = false;
}

/// <summary>
/// 用户状态枚举，用于标识用户的状态
/// </summary>
public enum UserStatus {
    /// <summary>
    /// 用户状态：活跃
    /// </summary>
    Active = 1,
    /// <summary>
    /// 用户状态：禁用
    /// </summary>
    Inactive = 0,
}
