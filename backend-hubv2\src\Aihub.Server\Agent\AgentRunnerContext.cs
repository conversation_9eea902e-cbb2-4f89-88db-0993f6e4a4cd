using System.Text.Json;
using Aihub.Server.Contexts;
using Aihub.Server.Converters;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Llm;
using Aihub.Server.Models;
using Aihub.Server.Models.Dto;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using Aihub.Server.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Agent;

public class AgentRunnerContext : ITransientService
{
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly ICurrentSessionIdAccessor _currentSessionIdAccessor;
    private readonly ICurrentAgentIdAccessor _currentAgentIdAccessor;
    private readonly SubAgentsDiscovery _subAgentsDiscovery;
    private readonly ChatHubWrapper _chatHubWrapper;
    private readonly AihubOption _aiHubOption;
    private readonly ToolExecutor _toolExecutor;
    public AgentRunnerContext(
        ToolExecutor toolExecutor,
        SubAgentsDiscovery subAgentsDiscovery,
        ICurrentSessionIdAccessor currentSessionIdAccessor,
        ICurrentAgentIdAccessor currentAgentIdAccessor,
        IDbContextFactory<AihubDbContext> dbContextFactory,
        ChatHubWrapper chatHubWrapper,
        IOptions<AihubOption> aiHubOption
    )
    {
        _dbContextFactory = dbContextFactory;
        _currentSessionIdAccessor = currentSessionIdAccessor;
        _currentAgentIdAccessor = currentAgentIdAccessor;
        _subAgentsDiscovery = subAgentsDiscovery;
        _chatHubWrapper = chatHubWrapper;
        _aiHubOption = aiHubOption.Value;
        _toolExecutor = toolExecutor;
    }


    public async Task<List<Message>> GetLlmAwareHistoryMessagesAsync()
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        var messages = await dbContext.StoredMessages
                .Where(m => m.SessionId == _currentSessionIdAccessor.GetSessionId())
                .OrderBy(m => m.CreatedAt)
                .Select(m => StoredToMessageConverter.ConvertToMessage(m))
                .ToListAsync();

        return messages.Where(w => w is UserMessage || w is AssistantMessage || w is SystemMessage || w is ToolMessage).ToList();
    }

    public async Task<List<SubAgentOptionWithDescription>> GetAvailableSubAgentOptionsAsync()
    {

        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var subAgentOptions = await dbContext.AgentOptions.Where(o => o.ParentId == _currentAgentIdAccessor.GetAgentId()).ToListAsync();
        var subAgentOptionsWithDescriptions = new List<SubAgentOptionWithDescription>();

        foreach (var subAgentOption in subAgentOptions)
        {
            var subAgentDescription = _subAgentsDiscovery.DiscoverTools().FirstOrDefault(d => d.Key == subAgentOption.Key);
            if (subAgentDescription is null)
            {
                throw new Exception($"Sub agent description not found for key: {subAgentOption.Key}");
            }
            subAgentOptionsWithDescriptions.Add(new SubAgentOptionWithDescription
            {
                StoredAgentOptionId = subAgentOption.Id,
                SubAgentDescription = subAgentDescription
            });
        }

        return subAgentOptionsWithDescriptions;
    }

    public async Task<Guid> SendBeginStreamingMessage()
    {
        var messageId = Guid.CreateVersion7();

        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var message = new StoredMessage
        {
            Id = messageId,
            SessionId = _currentSessionIdAccessor.GetSessionId() ?? throw new Exception("SessionId is null"),
            Category = StoredMessageCategory.Assistant,
            CreatedAt = DateTime.UtcNow,
            Content = string.Empty,
            Model = _aiHubOption.DefaultModel
        };
        dbContext.StoredMessages.Add(message);
        await dbContext.SaveChangesAsync();

        await _chatHubWrapper.SendBeginStreamingMessage(messageId);
        return messageId;
    }

    public async Task SendStreamingMessage(Guid messageId, string chunk, string content)
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        await dbContext.StoredMessages
            .Where(m => m.Id == messageId)
            .ExecuteUpdateAsync(u => u.SetProperty(m => m.Content, content)
        );
        await dbContext.SaveChangesAsync();

        await _chatHubWrapper.SendStreamingMessage(messageId, chunk);
    }

    public async Task SendEndStreamingMessage(Guid messageId, string content, List<AssistantMessageExtraToolCall> toolCalls, int inputTokenCount, int outputTokenCount)
    {
        var sessionId = _currentSessionIdAccessor.GetSessionId() ?? throw new Exception("SessionId is null");
        var toolCallsVo = await AgentRunnerContextHelper.HandleBlockMessage(_dbContextFactory, _chatHubWrapper, _aiHubOption.DefaultModel, sessionId, messageId, content, toolCalls, inputTokenCount, outputTokenCount);

        await _chatHubWrapper.SendEndStreamingMessage(messageId, content, toolCallsVo.ToList());

    }


    public async Task<bool> ExecuteToolCallsIfAllConfirmed(CancellationToken cancellationToken)
    {
        var sessionId = _currentSessionIdAccessor.GetSessionId() ?? throw new Exception("SessionId is null");
        return await AgentRunnerContextHelper.ExecuteToolCallsIfAllConfirmed(_dbContextFactory, sessionId, _toolExecutor, cancellationToken);
    }
}
