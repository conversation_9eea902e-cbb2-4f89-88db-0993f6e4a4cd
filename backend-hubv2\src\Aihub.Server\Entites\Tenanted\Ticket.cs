using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites.Tenanted;

public class Ticket {
    [Key]
    [Comment("票据ID")]
    public required Guid Id { get; set; }

    [Comment("用户键")]
    public required string UserKey {get;set;}
    
    [Comment("票据码")]
    public required string TicketCode { get; set; }

    [Comment("应用程序ID")]
    public required Guid ApplicationId { get; set; }

    [Comment("是否已使用")]
    public bool IsUsed { get; set; } = false;

    [Comment("票据类型")]
    public required TicketType TicketType { get; set; } = TicketType.OneTime;

    [Comment("过期时间")]
    public required DateTime ExpiredAt { get; set; }

    [Comment("创建时间")]
    public required DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 关联的租户ID，标识该客户端属于哪个租户
    /// </summary>
    [ForeignKey(nameof(Tenant))]
    [Comment("关联的租户ID，标识该客户端属于哪个租户")]
    public required Guid TenantId { get; set; }
    
    /// <summary>
    /// 关联的租户实体导航属性
    /// </summary>
    [Comment("关联的租户实体导航属性")]
    public Tenant Tenant { get; set; } = null!;
}

public enum TicketType {
    OneTime,
    Forever,
}