/**
 * Improved centralized error handling utilities with configurable message mapping
 */

export interface ErrorInfo {
  message: string;
  context?: string;
  error?: Error;
  severity?: 'low' | 'medium' | 'high';
  userFriendlyMessage?: string; // Allow override of user message
}

export interface ErrorMessageMapping {
  [key: string]: string;
}

export interface ErrorHandlerConfig {
  developmentMode?: boolean;
  defaultUserMessage?: string;
  messageMapping?: ErrorMessageMapping;
  contextMapping?: { [context: string]: string };
}

/**
 * Improved error handler class with configuration support
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private config: Required<ErrorHandlerConfig>;

  private constructor(config: ErrorHandlerConfig = {}) {
    this.config = {
      developmentMode: process.env.NODE_ENV === 'development',
      defaultUserMessage: 'Something went wrong. Please try again.',
      messageMapping: {
        // Network-related errors
        'network': 'Connection problem. Please check your internet connection and try again.',
        'connection': 'Connection problem. Please check your internet connection and try again.',
        'timeout': 'The operation timed out. Please try again.',
        'fetch': 'Network request failed. Please try again.',
        
        // Authentication errors
        'unauthorized': 'Authentication failed. Please refresh the page and try again.',
        'auth': 'Authentication failed. Please refresh the page and try again.',
        'forbidden': 'You don\'t have permission to perform this action.',
        
        // Server errors
        'server': 'Server error occurred. Please try again later.',
        'internal': 'Internal server error. Please try again later.',
        '500': 'Server error occurred. Please try again later.',
        '502': 'Service temporarily unavailable. Please try again later.',
        '503': 'Service temporarily unavailable. Please try again later.',
        
        // Client errors
        'bad request': 'Invalid request. Please check your input and try again.',
        '400': 'Invalid request. Please check your input and try again.',
        '404': 'Requested resource not found.',
        
        // Validation errors
        'validation': 'Please check your input and try again.',
        'invalid': 'Invalid input provided. Please correct and try again.',
      },
      contextMapping: {
        'SignalR Connection': 'Unable to connect to the AI service. Please try again in a moment.',
        'Message Send': 'Failed to send message. Please try again.',
        'Tool Confirmation': 'Failed to process tool confirmation. Please try again.',
        'Session Reset': 'Failed to reset session. Please try again.',
        'New Session': 'Failed to create new session. Please try again.',
      },
      ...config,
      messageMapping: { ...this.getDefaultMessageMapping(), ...config.messageMapping },
      contextMapping: { ...this.getDefaultContextMapping(), ...config.contextMapping },
    };
  }

  public static getInstance(config?: ErrorHandlerConfig): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler(config);
    }
    return ErrorHandler.instance;
  }

  /**
   * Update configuration after initialization
   */
  public updateConfig(config: Partial<ErrorHandlerConfig>): void {
    this.config = {
      ...this.config,
      ...config,
      messageMapping: { ...this.config.messageMapping, ...config.messageMapping },
      contextMapping: { ...this.config.contextMapping, ...config.contextMapping },
    };
  }

  /**
   * Handle errors with consistent logging and user notification
   */
  handleError(errorInfo: ErrorInfo, onError?: (message: string) => void): void {
    const { message, context, error, severity = 'medium', userFriendlyMessage } = errorInfo;
    
    // Format the error message
    const formattedMessage = context ? `${context}: ${message}` : message;
    
    // Log error for development/debugging
    if (this.config.developmentMode) {
      const logMethod = severity === 'high' ? 'error' : severity === 'medium' ? 'warn' : 'info';
      console[logMethod](`[${severity.toUpperCase()}] ${formattedMessage}`, error);
    }
    
    // Notify user if callback provided
    if (onError) {
      const finalUserMessage = userFriendlyMessage || 
        this.getUserFriendlyMessage(message, context);
      onError(finalUserMessage);
    }
  }

  /**
   * Convert technical error messages to user-friendly ones
   */
  private getUserFriendlyMessage(message: string, context?: string): string {
    const lowerMessage = message.toLowerCase();
    
    // Check context mapping first
    if (context && this.config.contextMapping[context]) {
      return this.config.contextMapping[context];
    }
    
    // Check message mapping
    for (const [key, friendlyMessage] of Object.entries(this.config.messageMapping)) {
      if (lowerMessage.includes(key.toLowerCase())) {
        return friendlyMessage;
      }
    }
    
    // Return default message
    return this.config.defaultUserMessage;
  }

  private getDefaultMessageMapping(): ErrorMessageMapping {
    return {
      'network': 'Connection problem. Please check your internet connection and try again.',
      'connection': 'Connection problem. Please check your internet connection and try again.',
      'timeout': 'The operation timed out. Please try again.',
      'unauthorized': 'Authentication failed. Please refresh the page and try again.',
      'auth': 'Authentication failed. Please refresh the page and try again.',
    };
  }

  private getDefaultContextMapping(): { [context: string]: string } {
    return {
      'SignalR Connection': 'Unable to connect to the AI service. Please try again in a moment.',
      'Message Send': 'Failed to send message. Please try again.',
      'Tool Confirmation': 'Failed to process tool confirmation. Please try again.',
    };
  }
}

// Export singleton instance with default configuration
export const errorHandler = ErrorHandler.getInstance();

// Export factory function for custom configurations
export const createErrorHandler = (config: ErrorHandlerConfig) => 
  ErrorHandler.getInstance(config);