using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites.Tenanted;

/// <summary>
/// 会话实体，代表一次完整的用户与AI助手的对话会话
/// 每个会话包含多条消息，用于组织和管理对话历史
/// </summary>
public class Session {
    /// <summary>
    /// 会话的唯一标识符
    /// </summary>
    [Key]
    [Comment("会话的唯一标识符")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 会话类型
    /// </summary>
    [Comment("会话类型")]
    public required SessionType Type { get; set; }

    /// <summary>
    /// 关联的代理选项ID
    /// </summary>
    [ForeignKey(nameof(AgentOption))]
    [Comment("关联的代理选项ID")]
    public required Guid AgentOptionId { get; set; }

    /// <summary>
    /// 关联的代理选项实体导航属性
    /// </summary>
    [Comment("关联的代理选项实体导航属性")]
    public AgentOption? AgentOption { get; set; }

    /// <summary>
    /// 关联的应用程序ID
    /// </summary>
    [ForeignKey(nameof(Application))]
    [Comment("关联的应用程序ID")]
    public Guid? ApplicationId { get; set; }

    /// <summary>
    /// 关联的应用程序实体导航属性
    /// </summary>
    [Comment("关联的应用程序实体导航属性")]
    public Application? Application { get; set; } = null!;

    /// <summary>
    /// 关联的客户端身份ID，标识该会话属于哪个客户端
    /// </summary>
    [ForeignKey(nameof(ClientIdentity))]
    [Comment("关联的客户端身份ID，标识该会话属于哪个客户端")]
    public required Guid ClientIdentityId { get; set; }
    
    /// <summary>
    /// 关联的客户端身份实体导航属性
    /// </summary>
    [Comment("关联的客户端身份实体导航属性")]
    public ClientIdentity ClientIdentity { get; set; } = null!;

    /// <summary>
    /// 父消息ID(SubAgent 时，父消息ID 为 Assistant 消息ID)
    /// </summary>
    [Comment("父消息ID(SubAgent 时，父消息ID 为 Assistant 消息ID)")]
    public Guid? ParentMessageId { get; set; }

    /// <summary>
    /// 父会话ID(SubAgent 时，父会话ID 为 父会话ID)
    /// </summary>
    [Comment("父会话ID(SubAgent 时，父会话ID 为 父会话ID)")]
    public Guid? ParentSessionId { get; set; }

    
    /// <summary>
    /// 会话创建时间，标记对话开始的时间点
    /// </summary>
    [Comment("会话创建时间，标记对话开始的时间点")]
    public required DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 会话最后更新时间，记录最后一次消息交互的时间
    /// </summary>
    [Comment("会话最后更新时间，记录最后一次消息交互的时间")]
    public required DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 该会话下的所有消息集合，包含用户和助手的完整对话历史
    /// </summary>
    [Comment("该会话下的所有消息集合，包含用户和助手的完整对话历史")]
    public ICollection<StoredMessage> Messages { get; set; } = new List<StoredMessage>();
}

public enum SessionType {
    Chat,
    SubAgent,
}