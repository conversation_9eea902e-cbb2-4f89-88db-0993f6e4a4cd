
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data.Common;
using System.Text.Json;
using Aihub.Server.Agent.SubAgents;
using Aihub.Server.Agent.Tool;
using Aihub.Server.Contexts;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Options;
using Microsoft.EntityFrameworkCore;
using MySql.Data.MySqlClient;

namespace Aihub.Server.Agent.SubAgents;

public class DatabaseSubAgentQuerySqlTool : ITool {

    private readonly SubAgentContextManager _subAgentContextManager;
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly ILogger _logger;

    public DatabaseSubAgentQuerySqlTool(
        SubAgentContextManager subAgentContextManager,
        IDbContextFactory<AihubDbContext> dbContextFactory,
        ILogger<DatabaseSubAgentQuerySqlTool> logger){
        _subAgentContextManager = subAgentContextManager;
        _dbContextFactory = dbContextFactory;
        _logger = logger;
    }

    private async Task<DbConnection> GetDbConnection(AgentOptionDatabaseAgentExtra extra){
        switch(extra.DbType){
            case AgentOptionDatabaseAgentExtraDbType.MySql:
                var connection = new MySqlConnection(extra.DbConnectionString);
                await connection.OpenAsync();
                return connection;
            default:
                throw new InvalidOperationException("Unsupported database type");
        }
    }

    [Tool("query_sql",
        """
        query_sql is a tool that executes a SQL query on the database.
        It takes a single parameter, sql, and returns a json with the result.
        The result is a list of dictionaries, each dictionary is a row of the result.
        Supports SQL statements that begin with SELECT.
        """,
        NameForUser = "查询数据",
        DescriptionForUser = "智能体通过内部接口查询数据",
        NeedConfirm = false,
        UserAware = true,
        Severity = ToolSeverity.Medium
    )]
    public async Task<object> QuerySql(
        [Required]
        [Description("The SQL query to execute.")]
        string sql){
        
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var agentOption = await dbContext.AgentOptions.FirstOrDefaultAsync(ai => ai.Id == _subAgentContextManager.GetSubAgentId());
        var extra = agentOption?.Extra is not null ? JsonSerializer.Deserialize<AgentOptionDatabaseAgentExtra>(agentOption.Extra, StandardizedJsonOption.DefaultOptions) : null;
    
        _logger.LogInformation($"On Agent {agentOption?.Id} Executing SQL: {sql}");

        using var connection = await GetDbConnection(extra!);
        using var command = connection.CreateCommand();
        command.CommandText = sql;

        using var reader = await command.ExecuteReaderAsync();
        var result = new List<Dictionary<string, object>>();
        while(await reader.ReadAsync()){
            var row = new Dictionary<string, object>();
            for(var i = 0; i < reader.FieldCount; i++){
                row[reader.GetName(i)] = reader.GetValue(i);
            }
            result.Add(row);
        }

        _logger.LogInformation($"On Agent {agentOption?.Id} SQL Result Length: {result.Count}, Column Length: {reader.FieldCount}, First Row: {JsonSerializer.Serialize(result.FirstOrDefault())}");

        return result;
        
    }
}
