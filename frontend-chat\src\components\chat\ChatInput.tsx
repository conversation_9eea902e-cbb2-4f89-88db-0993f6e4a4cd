import { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send, Loader2, Paperclip, Image, FileText, Settings, Mic, MicOff } from "lucide-react";
import { cn } from "@/lib/utils";
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
}

export const ChatInput = ({ 
  onSendMessage, 
  isLoading = false,
  placeholder = "Type your message..." 
}: ChatInputProps) => {
  const [message, setMessage] = useState("");
  const [attachments, setAttachments] = useState<File[]>([]);
  const [autoSend, setAutoSend] = useState(false);
  const [includeContext, setIncludeContext] = useState(true);
  const [isListening, setIsListening] = useState(false);
  const [speechSupported, setSpeechSupported] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  // Auto-resize textarea based on content
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset to auto to get accurate scrollHeight
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const minHeight = 48; // 3rem = 48px
      const maxHeight = 128; // 8rem = 128px
      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
      textarea.style.height = newHeight + 'px';
    }
  };

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || (window as Window & { webkitSpeechRecognition?: typeof SpeechRecognition }).webkitSpeechRecognition;
      if (SpeechRecognition) {
        setSpeechSupported(true);
        const recognition = new SpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = 'zh-CN';
        
        recognition.onstart = () => {
          setIsListening(true);
        };
        
        recognition.onresult = (event: SpeechRecognitionEvent) => {
          let interimTranscript = '';
          let finalTranscript = '';
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscript += transcript;
            } else {
              interimTranscript += transcript;
            }
          }
          
          if (finalTranscript) {
            setMessage(prev => prev + finalTranscript);
          }
        };
        
        recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
        };
        
        recognition.onend = () => {
          setIsListening(false);
        };
        
        recognitionRef.current = recognition;
      }
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []);

  // Toggle speech recognition
  const toggleSpeechRecognition = useCallback(() => {
    if (!recognitionRef.current || !speechSupported) return;
    
    if (isListening) {
      // Immediately update UI state for better responsiveness
      setIsListening(false);
      recognitionRef.current.stop();
    } else {
      // Immediately update UI state for better responsiveness
      setIsListening(true);
      recognitionRef.current.start();
    }
  }, [isListening, speechSupported]);

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  const handleFileSelect = (type: 'image' | 'document') => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = type === 'image' ? 'image/*' : '.pdf,.doc,.docx,.txt';
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if ((message.trim() || attachments.length > 0) && !isLoading) {
      onSendMessage(message.trim());
      setMessage("");
      setAttachments([]);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="border-t bg-chat-header-bg p-2 flex-shrink-0">
      {/* File attachments preview */}
      {attachments.length > 0 && (
        <div className="mb-1 flex flex-wrap gap-1">
          {attachments.map((file, index) => (
            <div 
              key={index}
              className="flex items-center gap-1 bg-muted rounded-lg px-1.5 py-0.5 text-xs"
            >
              {file.type.startsWith('image/') ? (
                <Image className="h-3 w-3" />
              ) : (
                <FileText className="h-3 w-3" />
              )}
              <span className="max-w-[80px] truncate">{file.name}</span>
              <button
                onClick={() => removeAttachment(index)}
                className="text-muted-foreground hover:text-foreground text-sm"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="flex flex-row gap-1.5 rounded-2xl border border-input bg-chat-input-bg p-2">
        <div className="flex-1 chat-input p-1">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => {
              setMessage(e.target.value);
              // Trigger height adjustment on next frame to ensure DOM is updated
              setTimeout(adjustTextareaHeight, 0);
            }}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="resize-none border-0 bg-transparent min-h-[3rem] max-h-32 focus-visible:ring-0 focus-visible:ring-offset-0 custom-scrollbar p-0 w-full"
            disabled={isLoading}
          />
        </div>
        
        {/* Speech Recognition button */}
        {speechSupported && (
          <Button
            type="button"
            size="icon"
            variant="ghost"
            onClick={toggleSpeechRecognition}
            className={cn(
              "rounded-full shrink-0",
              isListening ? "bg-red-500 hover:bg-red-600 text-white" : "hover:bg-muted"
            )}
            disabled={isLoading}
          >
            {isListening ? (
              <MicOff className="h-5 w-5 text-white" />
            ) : (
              <Mic className="h-5 w-5" />
            )}
          </Button>
        )}
        
        {/* Send button */}
        <Button
          type="submit"
          size="icon"
          className={cn(
            "rounded-full shrink-0 theme-toggle",
            "disabled:opacity-50"
          )}
          disabled={(!message.trim() && attachments.length === 0) || isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-5 w-5 animate-spin" />
          ) : (
            <Send className="h-5 w-5" />
          )}
        </Button>


      </form>
      
      {/* Disclaimer text */}
      <div className="text-center mt-2">
        <p className="text-xs text-muted-foreground">人工智能可能会犯错。请核查重要信息。</p>
      </div>
    </div>
  );
};