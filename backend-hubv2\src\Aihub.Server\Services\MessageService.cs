using Aihub.Server.Converters;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Models;
using Aihub.Server.Scrutors;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Services;

public class MessageService : ITransientService {

    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;

    public MessageService(IDbContextFactory<AihubDbContext> dbContextFactory){
        _dbContextFactory = dbContextFactory;
    }

    public async Task<IEnumerable<Message>> GetUserAwareHistoryMessages(Guid sessionId){

        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        var messages = await dbContext.StoredMessages
            .Where(m => m.SessionId == sessionId)
            .Where(m => m.Category == StoredMessageCategory.User || m.Category == StoredMessageCategory.Assistant || m.Category == StoredMessageCategory.SubAgentAssistant)
            .OrderBy(m => m.CreatedAt)
            .ToListAsync();

        return messages.Select(m => StoredToMessageConverter.ConvertToMessage(m));
    }

    public async Task CreateUserMessage(Guid sessionId, string message){
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var storedMessage = new StoredMessage{
            Id = Guid.CreateVersion7(),
            SessionId = sessionId,
            Content = message,
            Category = StoredMessageCategory.User,
            CreatedAt = DateTime.UtcNow,
        };
        dbContext.StoredMessages.Add(storedMessage);
        await dbContext.SaveChangesAsync();
    }

}
