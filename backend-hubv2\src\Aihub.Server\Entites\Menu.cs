using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites;

/// <summary>
/// 菜单实体，用于存储菜单信息
/// </summary>
public class Menu {

    /// <summary>
    /// 菜单ID
    /// </summary>
    [Key]
    [Comment("菜单ID")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 父菜单ID
    /// </summary>
    [Comment("父菜单ID")]
    public required Guid ParentId { get; set; }

    /// <summary>
    /// 父节点ID路径
    /// </summary>
    [Comment("父节点ID路径")]
    public string? TreePath { get; set; }

    /// <summary>
    /// 菜单名称
    /// </summary>
    [Comment("菜单名称")]
    public required string Name { get; set; } = string.Empty;

    /// <summary>
    /// 菜单类型（1-菜单 2-目录 3-外链 4-按钮）
    /// </summary>
    [Comment("菜单类型（1-菜单 2-目录 3-外链 4-按钮）")]
    public required MenuType Type { get; set; }

    /// <summary>
    /// 路由名称（Vue Router 中用于命名路由）
    /// </summary>
    [Comment("路由名称（Vue Router 中用于命名路由）")]
    public string? RouteName { get; set; }

    /// <summary>
    /// 路由路径（Vue Router 中定义的 URL 路径）
    /// </summary>
    [Comment("路由路径（Vue Router 中定义的 URL 路径）")]
    public string? RoutePath { get; set; }

    /// <summary>
    /// 组件路径（组件页面完整路径，相对于 src/views/，缺省后缀 .vue）
    /// </summary>
    [Comment("组件路径（组件页面完整路径，相对于 src/views/，缺省后缀 .vue）")]
    public string? Component { get; set; }

    /// <summary>
    /// 【按钮】权限标识
    /// </summary>
    [Comment("【按钮】权限标识")]
    public string? Perm { get; set; }

    /// <summary>
    /// 【目录】只有一个子路由是否始终显示（1-是 0-否）
    /// </summary>
    [Comment("【目录】只有一个子路由是否始终显示（1-是 0-否）")]
    public bool AlwaysShow { get; set; } = false;

    /// <summary>
    /// 【菜单】是否开启页面缓存（1-是 0-否）
    /// </summary>
    [Comment("【菜单】是否开启页面缓存（1-是 0-否）")]
    public bool KeepAlive { get; set; } = false;

    /// <summary>
    /// 显示状态（1-显示 0-隐藏）
    /// </summary>
    [Comment("显示状态（1-显示 0-隐藏）")]
    public bool Visible { get; set; } = true;

    /// <summary>
    /// 排序
    /// </summary>
    [Comment("排序")]
    public int Sort { get; set; } = 0;

    /// <summary>
    /// 菜单图标
    /// </summary>
    [Comment("菜单图标")]
    public string? Icon { get; set; }

    /// <summary>
    /// 跳转路径
    /// </summary>
    [Comment("跳转路径")]
    public string? Redirect { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Comment("创建时间")]
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Comment("更新时间")]
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 路由参数
    /// </summary>
    [Comment("路由参数")]
    public string? Params { get; set; }

}


/// <summary>
/// 菜单类型枚举，用于标识菜单的类型
/// </summary>
public enum MenuType {
    /// <summary>
    /// 菜单
    /// </summary>
    Menu = 1,
    /// <summary>
    /// 目录
    /// </summary>
    Directory = 2,
    /// <summary>
    /// 外链
    /// </summary>
    External = 3,
    /// <summary>
    /// 按钮
    /// </summary>
    Button = 4,
}