import { <PERSON><PERSON> } from "@/components/ui/button";
import { Moon, Sun, RotateCcw, LogOut, Wifi, WifiOff } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "@/hooks/use-theme";
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from "@/components/ui/alert-dialog";
import { useState } from "react";

import type { ChatHeaderProps } from '@/types/chat';
import { HubConnectionState } from "@microsoft/signalr";

export const ChatHeader = ({ onReset, profile, connectionStatus = HubConnectionState.Disconnected }: ChatHeaderProps) => {
  const { theme, toggleTheme } = useTheme();
  const [isResetDialogOpen, setIsResetDialogO<PERSON>] = useState(false);
  
  // Check if theme switching is allowed (profile.theme should not be set)
  const isThemeSwitchingAllowed = !profile?.theme;

  return (
    <div className="border-b bg-chat-header-bg p-2 sm:p-4 flex items-center justify-between flex-shrink-0">
      <div className="flex items-center gap-2 sm:gap-3">
        <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-gradient-to-br from-primary to-primary-glow flex items-center justify-center overflow-hidden">
          {profile?.icon ? (
            <img 
              src={profile.icon} 
              alt={profile?.displayName} 
              className="h-full w-full object-cover"
            />
          ) : (
            <span className="text-primary-foreground font-semibold text-xs sm:text-sm">
              {profile?.displayName?.substring(0, 2)?.toUpperCase() || 'AI'}
            </span>
          )}
        </div>
        <div className="min-w-0 flex-1">
          <div className="flex items-center gap-2">
            <h1 className="font-semibold text-sm sm:text-lg truncate">{profile?.displayName || '智能助手'}</h1>
            <Badge 
              variant={connectionStatus === HubConnectionState.Connected && profile?.fetched ? 'default' : 'secondary'}
              className={`text-xs flex-shrink-0 ${
                connectionStatus === HubConnectionState.Connected && profile?.fetched
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}
            >
              {connectionStatus === HubConnectionState.Connected && profile?.fetched ? (
                <>
                  <span className="block sm:hidden"><Wifi className="h-3 w-3" /></span>
                  <span className="hidden sm:inline-flex"><Wifi className="h-3 w-3 mr-1" />已连接</span>
                </>
              ) : (
                <>
                  <span className="block sm:hidden"><WifiOff className="h-3 w-3" /></span>
                  <span className="hidden sm:inline-flex"><WifiOff className="h-3 w-3 mr-1" />离线</span>
                </>
              )}
            </Badge>
          </div>
          <p className="text-xs sm:text-sm text-muted-foreground truncate hidden sm:block">
            {connectionStatus === HubConnectionState.Connected && profile?.fetched ? profile?.description || '智能助手在线为您服务' : '离线状态'}
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-1 sm:gap-2">
        <AlertDialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 sm:h-10 sm:w-10 rounded-full"
              title="重置会话"
              disabled={connectionStatus !== HubConnectionState.Connected || !profile?.fetched}
            >
              <RotateCcw className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>重置会话</AlertDialogTitle>
              <AlertDialogDescription>
                确定要重置当前会话吗？这将清除所有对话记录并返回到初始状态。此操作无法撤销。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction 
                onClick={() => {
                  onReset?.();
                  setIsResetDialogOpen(false);
                }}
              >
                确认重置
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
        
        {profile?.signOutUrl && (
          <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            if (profile?.signOutUrl) {
              window.location.href = profile.signOutUrl;
            } else {
              console.log('用户登出');
            }
          }}
          className="h-8 w-8 sm:h-10 sm:w-10 rounded-full"
          title="退出登录"
        >
          <LogOut className="h-4 w-4 sm:h-5 sm:w-5" />
        </Button>)}

        {isThemeSwitchingAllowed && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            className="theme-toggle h-8 w-8 sm:h-10 sm:w-10 rounded-full"
          >
            {theme === "dark" ? (
              <Sun className="h-4 w-4 sm:h-5 sm:w-5" />
            ) : (
              <Moon className="h-4 w-4 sm:h-5 sm:w-5" />
            )}
          </Button>
        )}
      </div>
    </div>
  );
};