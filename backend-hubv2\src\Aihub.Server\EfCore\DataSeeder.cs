using System.Text.Json;
using Aihub.Server.EfCore;
using Aihub.Server.Entites;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.EfCore;

public class DataSeeder : ITransientService
{
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;

    public DataSeeder(IDbContextFactory<AihubDbContext> dbContextFactory)
    {
        _dbContextFactory = dbContextFactory;
    }

    public async Task SeedAsync()
    {
        using var dbContext = _dbContextFactory.CreateDbContext();

        await SeedTenantsAsync(dbContext);
        await SeedUsersAsync(dbContext);
        await SeedRolesAsync(dbContext);
        await SeedMenusAsync(dbContext);
        await SeedRoleMenusAsync(dbContext);
        await SeedUserRolesAsync(dbContext);
        await SeedApplicationsAsync(dbContext);
        await SeedAgentOptionsAsync(dbContext);
        await SeedTicketsAsync(dbContext);

        await dbContext.SaveChangesAsync();
    }

    private async Task SeedTenantsAsync(AihubDbContext dbContext)
    {
        if (await dbContext.Tenants.AnyAsync())
            return;

        var tenants = new[]
        {
            new Tenant
            {
                Id = Guid.CreateVersion7(),
                Key = "default",
                Name = "默认租户",
                Description = "系统默认租户",
                CreatedAt = DateTime.UtcNow
            },
            new Tenant
            {
                Id = Guid.CreateVersion7(),
                Key = "demo",
                Name = "演示租户",
                Description = "用于演示的租户",
                CreatedAt = DateTime.UtcNow
            }
        };

        await dbContext.Tenants.AddRangeAsync(tenants);
    }

    private async Task SeedUsersAsync(AihubDbContext dbContext)
    {
        if (await dbContext.Users.AnyAsync())
            return;

        var users = new[]
        {
            new User
            {
                Id = Guid.CreateVersion7(),
                Username = "admin",
                Nickname = "系统管理员",
                Gender = 1,
                Password = "admin123", // 实际应用中应该使用哈希密码
                Status = UserStatus.Active,
                CreateTime = DateTime.UtcNow
            },
            new User
            {
                Id = Guid.CreateVersion7(),
                Username = "user",
                Nickname = "普通用户",
                Gender = 1,
                Password = "user123",
                Status = UserStatus.Active,
                CreateTime = DateTime.UtcNow
            }
        };

        await dbContext.Users.AddRangeAsync(users);
    }

    private async Task SeedRolesAsync(AihubDbContext dbContext)
    {
        if (await dbContext.Roles.AnyAsync())
            return;

        var roles = new[]
        {
            new Role
            {
                Id = Guid.CreateVersion7(),
                Name = "系统管理员",
                Code = "admin",
                Sort = 1,
                Status = RoleStatus.Active,
                DataScope = DataScope.All,
                CreateTime = DateTime.UtcNow
            },
            new Role
            {
                Id = Guid.CreateVersion7(),
                Name = "普通用户",
                Code = "user",
                Sort = 2,
                Status = RoleStatus.Active,
                DataScope = DataScope.Self,
                CreateTime = DateTime.UtcNow
            }
        };

        await dbContext.Roles.AddRangeAsync(roles);
    }

    private async Task SeedMenusAsync(AihubDbContext dbContext)
    {
        if (await dbContext.Menus.AnyAsync())
            return;

        var rootId = Guid.CreateVersion7();
        var systemManagementId = Guid.CreateVersion7();
        var userManagementId = Guid.CreateVersion7();
        var roleManagementId = Guid.CreateVersion7();
        var menuManagementId = Guid.CreateVersion7();

        var menus = new[]
        {
            // 根目录
            new Menu
            {
                Id = rootId,
                ParentId = Guid.Empty,
                TreePath = "0",
                Name = "系统管理",
                Type = MenuType.Directory,
                RoutePath = "/system",
                Icon = "system",
                Sort = 1,
                Visible = true,
                CreateTime = DateTime.UtcNow
            },
            // 用户管理
            new Menu
            {
                Id = userManagementId,
                ParentId = rootId,
                TreePath = $"0,{rootId}",
                Name = "用户管理",
                Type = MenuType.Menu,
                RouteName = "UserManagement",
                RoutePath = "/system/user",
                Component = "system/user/index",
                Icon = "user",
                Sort = 1,
                Visible = true,
                CreateTime = DateTime.UtcNow
            },
            // 角色管理
            new Menu
            {
                Id = roleManagementId,
                ParentId = rootId,
                TreePath = $"0,{rootId}",
                Name = "角色管理",
                Type = MenuType.Menu,
                RouteName = "RoleManagement",
                RoutePath = "/system/role",
                Component = "system/role/index",
                Icon = "role",
                Sort = 2,
                Visible = true,
                CreateTime = DateTime.UtcNow
            },
            // 菜单管理
            new Menu
            {
                Id = menuManagementId,
                ParentId = rootId,
                TreePath = $"0,{rootId}",
                Name = "菜单管理",
                Type = MenuType.Menu,
                RouteName = "MenuManagement",
                RoutePath = "/system/menu",
                Component = "system/menu/index",
                Icon = "menu",
                Sort = 3,
                Visible = true,
                CreateTime = DateTime.UtcNow
            }
        };

        await dbContext.Menus.AddRangeAsync(menus);
    }

    private async Task SeedRoleMenusAsync(AihubDbContext dbContext)
    {
        if (await dbContext.RoleMenus.AnyAsync())
            return;

        // 等待保存后获取实际的角色和菜单ID
        await dbContext.SaveChangesAsync();

        var adminRole = await dbContext.Roles.FirstAsync(r => r.Code == "admin");
        var menus = await dbContext.Menus.ToListAsync();

        var roleMenus = menus.Select(menu => new RoleMenu
        {
            Id = Guid.CreateVersion7(),
            RoleId = adminRole.Id,
            MenuId = menu.Id
        });

        await dbContext.RoleMenus.AddRangeAsync(roleMenus);
    }

    private async Task SeedUserRolesAsync(AihubDbContext dbContext)
    {
        if (await dbContext.UserRoles.AnyAsync())
            return;

        // 等待保存后获取实际的用户和角色ID
        await dbContext.SaveChangesAsync();

        var adminUser = await dbContext.Users.FirstAsync(u => u.Username == "admin");
        var normalUser = await dbContext.Users.FirstAsync(u => u.Username == "user");
        var adminRole = await dbContext.Roles.FirstAsync(r => r.Code == "admin");
        var userRole = await dbContext.Roles.FirstAsync(r => r.Code == "user");

        var userRoles = new[]
        {
            new UserRole { Id = Guid.CreateVersion7(), UserId = adminUser.Id, RoleId = adminRole.Id },
            new UserRole { Id = Guid.CreateVersion7(), UserId = normalUser.Id, RoleId = userRole.Id }
        };

        await dbContext.UserRoles.AddRangeAsync(userRoles);
    }

    private async Task SeedApplicationsAsync(AihubDbContext dbContext)
    {
        if (await dbContext.Applications.AnyAsync())
            return;

        var defaultTenant = await dbContext.Tenants.FirstAsync(t => t.Key == "default");

        var applications = new[]
        {
            new Application
            {
                Id = Guid.CreateVersion7(),
                Key = "default-app",
                DisplayName = "默认应用",
                Description = "系统默认应用",
                TenantId = defaultTenant.Id,
                RecommendedStartPrompts = new List<string>
                {
                    "请以表格的方式列出今年到期的合同",
                    "请按年月的方式统计出所有商机的数量",
                    "请统计这个月到期的付款记录列出具体的合同和逾期金额"
                },
                CreatedAt = DateTime.UtcNow
            }
        };

        await dbContext.Applications.AddRangeAsync(applications);
    }

    private async Task SeedAgentOptionsAsync(AihubDbContext dbContext)
    {
        if (await dbContext.AgentOptions.AnyAsync())
            return;

        var primaryAgentOption = new AgentOption
        {
            Id = Guid.CreateVersion7(),
            Key = "primary",
            Name = "主要代理",
            Description = "系统主要代理选项",
            CreatedAt = DateTime.UtcNow
        };

        var databaseAgentOption = new AgentOption
        {
            Id = Guid.CreateVersion7(),
            Key = "database-agent",
            Name = "数据库分析智能体",
            Description = "数据库查询代理选项",
            Extra = JsonSerializer.Serialize(new AgentOptionDatabaseAgentExtra
            {
                DbType = AgentOptionDatabaseAgentExtraDbType.MySql,
                DbConnectionString = "Server=localhost;Database=tsz-integrated-platform;User=root;Password=****",
                CachedSchema = 
                    """
                    # Database schema

                    ```sql
                    CREATE TABLE `contract` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `contract_no` varchar(100) NOT NULL COMMENT '合同编号',
                    `contract_name` varchar(200) NOT NULL COMMENT '合同名称',
                    `contract_type` varchar(50) DEFAULT NULL COMMENT '合同类型(关联字典编码：contract_type)',
                    `contract_category` varchar(50) DEFAULT NULL COMMENT '合同分类(关联字典编码：contract_category)',
                    `contract_amount` decimal(15,2) DEFAULT NULL COMMENT '合同金额',
                    `opportunity_id` bigint DEFAULT NULL COMMENT '关联商机ID(关联opportunity表)',
                    `signing_date` date DEFAULT NULL COMMENT '签署日期',
                    `effective_date` date DEFAULT NULL COMMENT '生效日期',
                    `expiry_date` date DEFAULT NULL COMMENT '到期日期',
                    `contract_status` varchar(20) DEFAULT 'draft' COMMENT '合同状态(draft-草稿 pending-待签署 active-已生效 completed-已完成 terminated-已终止 cancelled-已作废)',
                    `payment_method` varchar(50) DEFAULT NULL COMMENT '付款方式(关联字典编码：payment_method)',
                    `signing_location` varchar(200) DEFAULT NULL COMMENT '签署地点',
                    `responsible_user_id` bigint DEFAULT NULL COMMENT '负责人ID(关联sys_user表)',
                    `dept_id` bigint DEFAULT NULL COMMENT '所属部门ID(关联sys_dept表)',
                    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                    PRIMARY KEY (`id`) USING BTREE
                    ) COMMENT='合同主表';

                    CREATE TABLE `contract_attachment` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `contract_id` bigint NOT NULL COMMENT '合同ID(关联contract表)',
                    `attachment_id` bigint NOT NULL COMMENT '附件ID(关联tsz_attachment表)',
                    `sort` int DEFAULT '0' COMMENT '排序',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                    PRIMARY KEY (`id`) USING BTREE,
                    KEY `idx_contract_id` (`contract_id`) USING BTREE,
                    KEY `idx_attachment_id` (`attachment_id`) USING BTREE
                    ) COMMENT='合同文件关联表';

                    CREATE TABLE `contract_partner_relation` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `contract_id` bigint NOT NULL COMMENT '合同ID',
                    `partner_id` bigint NOT NULL COMMENT '伙伴ID',
                    `partner_role` varchar(20) NOT NULL COMMENT '伙伴角色(关联字典编码：partner_role)',
                    `partner_role_desc` varchar(20) DEFAULT NULL COMMENT '角色描述(关联字典编码：partner_role_desc)',
                    `signing_person` varchar(100) DEFAULT NULL COMMENT '签署人',
                    `signing_person_title` varchar(100) DEFAULT NULL COMMENT '签署人职务',
                    `signing_date` date DEFAULT NULL COMMENT '签署日期',
                    `sort` int DEFAULT '0' COMMENT '排序',
                    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    PRIMARY KEY (`id`) USING BTREE
                    ) COMMENT='合同伙伴关联表';

                    CREATE TABLE `contract_payment` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `contract_id` bigint NOT NULL COMMENT '合同ID',
                    `payment_no` varchar(100) DEFAULT NULL COMMENT '付款单号',
                    `payment_type` varchar(20) NOT NULL COMMENT '付款类型(关联字典编码：payment_type)',
                    `payment_method` varchar(50) DEFAULT NULL COMMENT '付款方式(关联字典编码：payment_method)',
                    `payer_partner_id` bigint DEFAULT NULL COMMENT '付款方ID(关联partner表)',
                    `payee_partner_id` bigint DEFAULT NULL COMMENT '收款方ID(关联partner表)',
                    `planned_amount` decimal(15,2) DEFAULT NULL COMMENT '计划金额',
                    `actual_amount` decimal(15,2) DEFAULT NULL COMMENT '实际金额',
                    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
                    `planned_date` date DEFAULT NULL COMMENT '计划付款日期',
                    `actual_date` date DEFAULT NULL COMMENT '实际付款日期',
                    `payment_status` varchar(20) DEFAULT 'pending' COMMENT '付款状态(pending-待付款 paid-已付款 partial-部分付款 overdue-已逾期 cancelled-已取消)',
                    `bank_name` varchar(200) DEFAULT NULL COMMENT '付款银行',
                    `bank_account` varchar(50) DEFAULT NULL COMMENT '付款账号',
                    `transaction_no` varchar(100) DEFAULT NULL COMMENT '交易流水号',
                    `voucher_attachment_id` varchar(500) DEFAULT NULL COMMENT '付款凭证文件Id',
                    `invoice_status` varchar(20) DEFAULT 'not_issued' COMMENT '发票状态(not_issued-未开票 issued-已开票 received-已收票)',
                    `invoice_no` varchar(100) DEFAULT NULL COMMENT '发票号码',
                    `invoice_amount` decimal(15,2) DEFAULT NULL COMMENT '发票金额',
                    `invoice_date` date DEFAULT NULL COMMENT '开票日期',
                    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                    PRIMARY KEY (`id`) USING BTREE
                    ) COMMENT='合同付款记录表';

                    CREATE TABLE `opportunity` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `opportunity_code` varchar(50) NOT NULL COMMENT '商机编码(自动生成)',
                    `opportunity_name` varchar(200) NOT NULL COMMENT '商机名称',
                    `opportunity_type` varchar(20) NOT NULL COMMENT '商机类型(关联字典编码：opportunity_type)',
                    `opportunity_source` varchar(20) DEFAULT NULL COMMENT '商机来源(关联字典编码：opportunity_source)',
                    `partner_id` bigint DEFAULT NULL COMMENT '关联客户ID(关联partner表)',
                    `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
                    `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
                    `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
                    `opportunity_stage` varchar(20) DEFAULT 'initial' COMMENT '商机阶段(initial-初步接触 interested-有意向 proposal-方案阶段 negotiation-谈判阶段 closed_won-成交 closed_lost-失败)',
                    `win_probability` int DEFAULT '0' COMMENT '成单概率(%)',
                    `estimated_amount` decimal(15,2) DEFAULT NULL COMMENT '预估金额',
                    `estimated_close_date` date DEFAULT NULL COMMENT '预计成交日期',
                    `actual_close_date` date DEFAULT NULL COMMENT '实际成交日期',
                    `opportunity_status` varchar(20) DEFAULT 'active' COMMENT '商机状态(active-进行中 won-已成交 lost-已失败 cancelled-已取消 archived-已归档)',
                    `lost_reason` varchar(20) DEFAULT NULL COMMENT '失败原因(关联字典编码：lost_reason)',
                    `priority` varchar(10) DEFAULT 'medium' COMMENT '优先级(high-高 medium-中 low-低)',
                    `product_interest` varchar(500) DEFAULT NULL COMMENT '感兴趣的产品/服务',
                    `requirements` text COMMENT '客户需求描述',
                    `competition_info` varchar(500) DEFAULT NULL COMMENT '竞争对手信息',
                    `next_action` varchar(500) DEFAULT NULL COMMENT '下一步行动计划',
                    `next_follow_date` date DEFAULT NULL COMMENT '下次跟进日期',
                    `responsible_user_id` bigint DEFAULT NULL COMMENT '负责人ID(关联sys_user表)',
                    `dept_id` bigint DEFAULT NULL COMMENT '所属部门ID(关联sys_dept表)',
                    `tags` varchar(200) DEFAULT NULL COMMENT '标签(多个标签用逗号分隔)',
                    `archive_reason` varchar(500) DEFAULT NULL COMMENT '归档原因',
                    `remark` text COMMENT '备注',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                    PRIMARY KEY (`id`) USING BTREE
                    ) COMMENT='商机线索表';

                    CREATE TABLE `opportunity_follow` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `opportunity_id` bigint NOT NULL COMMENT '商机ID',
                    `follow_type` varchar(20) NOT NULL COMMENT '跟进方式(关联字典编码：follow_type)',
                    `follow_date` datetime NOT NULL COMMENT '跟进时间',
                    `follow_duration` int DEFAULT NULL COMMENT '跟进时长(分钟)',
                    `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
                    `follow_content` text NOT NULL COMMENT '跟进内容',
                    `follow_result` varchar(20) DEFAULT NULL COMMENT '跟进结果(关联字典编码：follow_result)',
                    `next_action` varchar(500) DEFAULT NULL COMMENT '下一步行动计划',
                    `next_follow_date` date DEFAULT NULL COMMENT '下次跟进日期',
                    `attachment_id` varchar(500) DEFAULT NULL COMMENT '附件ID',
                    `follow_user_id` bigint DEFAULT NULL COMMENT '跟进人ID(关联sys_user表)',
                    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    PRIMARY KEY (`id`) USING BTREE
                    ) COMMENT='商机跟进记录表';

                    CREATE TABLE `partner` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `partner_name` varchar(200) NOT NULL COMMENT '伙伴名称',
                    `partner_code` varchar(50) DEFAULT NULL COMMENT '伙伴编码',
                    `is_our_company` tinyint DEFAULT '0' COMMENT '是否我司或旗下企业(1-是 0-否)',
                    `partner_type` varchar(20) NOT NULL COMMENT '伙伴类型(关联字典编码：partner_type)',
                    `legal_representative` varchar(100) DEFAULT NULL COMMENT '法定代表人',
                    `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
                    `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
                    `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
                    `address` varchar(500) DEFAULT NULL COMMENT '地址',
                    `certificate_type` varchar(20) DEFAULT NULL COMMENT '证件类型(关联字典编码：certificate_type)',
                    `certificate_number` varchar(50) DEFAULT NULL COMMENT '证件号码',
                    `tax_number` varchar(50) DEFAULT NULL COMMENT '税号',
                    `bank_name` varchar(200) DEFAULT NULL COMMENT '开户银行',
                    `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
                    `status` varchar(10) DEFAULT 'active' COMMENT '状态(active-正常 inactive-禁用)',
                    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                    PRIMARY KEY (`id`) USING BTREE
                    ) COMMENT='业务伙伴表';

                    CREATE TABLE `sys_dept` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `name` varchar(100) NOT NULL COMMENT '部门名称',
                    `code` varchar(100) NOT NULL COMMENT '部门编号',
                    `parent_id` bigint DEFAULT '0' COMMENT '父节点id',
                    `tree_path` varchar(255) NOT NULL COMMENT '父节点id路径',
                    `sort` smallint DEFAULT '0' COMMENT '显示顺序',
                    `status` tinyint DEFAULT '1' COMMENT '状态(1-正常 0-禁用)',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                    PRIMARY KEY (`id`) USING BTREE,
                    UNIQUE KEY `uk_code` (`code`) USING BTREE COMMENT '部门编号唯一索引'
                    ) COMMENT='部门表';

                    CREATE TABLE `sys_dict` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ',
                    `dict_code` varchar(50) DEFAULT NULL COMMENT '类型编码',
                    `name` varchar(50) DEFAULT NULL COMMENT '类型名称',
                    `status` tinyint(1) DEFAULT '0' COMMENT '状态(0:正常;1:禁用)',
                    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    `is_deleted` tinyint DEFAULT '0' COMMENT '是否删除(1-删除，0-未删除)',
                    PRIMARY KEY (`id`) USING BTREE,
                    KEY `idx_dict_code` (`dict_code`)
                    ) COMMENT='字典表';

                    CREATE TABLE `sys_dict_item` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `dict_code` varchar(50) DEFAULT NULL COMMENT '关联字典编码，与sys_dict表中的dict_code对应',
                    `value` varchar(50) DEFAULT NULL COMMENT '字典项值',
                    `label` varchar(100) DEFAULT NULL COMMENT '字典项标签',
                    `tag_type` varchar(50) DEFAULT NULL COMMENT '标签类型，用于前端样式展示（如success、warning等）',
                    `status` tinyint DEFAULT '0' COMMENT '状态（1-正常，0-禁用）',
                    `sort` int DEFAULT '0' COMMENT '排序',
                    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                    PRIMARY KEY (`id`) USING BTREE
                    ) COMMENT='字典项表';

                    CREATE TABLE `tsz_attachment` (
                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                    `file_name` varchar(255) NOT NULL COMMENT '文件名',
                    `file_path` varchar(255) NOT NULL COMMENT '文件路径',
                    `file_type` varchar(255) NOT NULL COMMENT '文件类型(IMAGE: 图片, VIDEO: 视频, DOCUMENT: 文档, AUDIO: 音频, OTHER: 其他)',
                    `file_size` bigint DEFAULT NULL COMMENT '文件大小',
                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                    `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                    `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
                    PRIMARY KEY (`id`) USING BTREE
                    ) COMMENT='附件表';
                    ```
                    
                    # Dictionary field description
                    If the comment of a field in a data table contains '关联字典编码：xxx', it means that the field is a field in the dictionary table
                    ```sql
                    SELECT value, label FROM sys_dict_item WHERE dict_code = 'xxx'
                    ```
                    Then use the queried values and labels to replace the corresponding label with '关联字典编码：xxx' in the comment.

                    # `is_deleted` field description
                    Please pay attention to the is_deleted field in the database table, which indicates that the record has been deleted and is hidden. Please filter out these records when querying.

                    # Other Information
                    Database Type: MySQL
                    Database Version: 8.0.1

                    # 数据库语法
                    请在所有 表名、列名（字段名）、别名 上使用反引号，避免出现关键字冲突。
                    
                    """
            }, StandardizedJsonOption.DefaultOptions),
            ParentId = primaryAgentOption.Id,
            CreatedAt = DateTime.UtcNow
        };

        var manualGuideAgentOption = new AgentOption
        {
            Id = Guid.CreateVersion7(),
            Key = "manual-guide-agent",
            Name = "操作方法指南智能体",
            Description = "操作方法指南智能体",
            Extra = JsonSerializer.Serialize(new AgentOptionManualGuideAgentExtra
            {
                ManualGuide = 
                """
                # 上下文
                系统名称：探数者CRM

                # 系统功能导航路径

                * 合同管理
                    * 合同列表
                        * 添加合同
                        * 编辑合同
                        * 查看合同详情
                        * 删除合同
                        * 付款列表
                            * 添加付款记录
                            * 编辑付款记录
                            * 查看付款记录详情
                            * 删除付款记录

                * 商机管理
                    * 商机列表
                        * 添加商机
                        * 编辑商机
                        * 查看商机详情
                        * 删除商机
                        * 跟进列表
                            * 添加跟进
                            * 编辑跟进
                            * 查看跟进详情
                            * 删除跟进

                """,
            }, StandardizedJsonOption.DefaultOptions),
            ParentId = primaryAgentOption.Id,
            CreatedAt = DateTime.UtcNow
        };

        await dbContext.AgentOptions.AddRangeAsync(new[] { primaryAgentOption, databaseAgentOption, manualGuideAgentOption });

        // 更新应用的AgentOptionId
        await dbContext.SaveChangesAsync();
        var defaultApplication = await dbContext.Applications.FirstAsync();
        defaultApplication.AgentOptionId = primaryAgentOption.Id;
    }

    private async Task SeedTicketsAsync(AihubDbContext dbContext)
    {
        if (await dbContext.Tickets.AnyAsync())
            return;

        var defaultTenant = await dbContext.Tenants.FirstAsync(t => t.Key == "default");
        var defaultApplication = await dbContext.Applications.FirstAsync();

        var ticket = new Ticket
        {
            Id = Guid.CreateVersion7(),
            UserKey = "tenant-user",
            TicketCode = "ah-sk-123",
            ApplicationId = defaultApplication.Id,
            TicketType = TicketType.Forever,
            ExpiredAt = DateTime.UtcNow.AddDays(1),
            TenantId = defaultTenant.Id,
            CreatedAt = DateTime.UtcNow
        };

        await dbContext.Tickets.AddAsync(ticket);
    }

}