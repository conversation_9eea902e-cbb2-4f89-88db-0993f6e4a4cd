# Environment Variables Template
# Copy this file to .env.local and modify the values for your local development setup

# SignalR Configuration
# The WebSocket hub URL for real-time communication
VITE_SIGNALR_HUB_URL=http://localhost:5000/chathub
# Maximum number of reconnection attempts
VITE_SIGNALR_RECONNECT_ATTEMPTS=3
# Connection timeout in milliseconds
VITE_SIGNALR_CONNECTION_TIMEOUT=30000
# Base delay for exponential backoff (milliseconds)
VITE_SIGNALR_BASE_RETRY_DELAY=1000
# Maximum retry delay (milliseconds)
VITE_SIGNALR_MAX_RETRY_DELAY=30000
# Exponential backoff multiplier
VITE_SIGNALR_EXPONENTIAL_BASE=2
# Maximum exponential retry attempts
VITE_SIGNALR_MAX_EXPONENTIAL_RETRIES=3

# UI Configuration
# Delay between characters during streaming (milliseconds)
VITE_UI_STREAMING_CHAR_DELAY=30
# Auto-scroll delay (milliseconds)
VITE_UI_AUTO_SCROLL_DELAY=100
# Toast notification duration (milliseconds)
VITE_UI_TOAST_DURATION=5000

# Message Configuration
# Maximum allowed content length for messages
VITE_MESSAGE_MAX_CONTENT_LENGTH=8000
# Prefix for message IDs
VITE_MESSAGE_ID_PREFIX=msg_

# Development Settings
# Enable debug mode (true/false)
VITE_DEBUG_MODE=false
# Logging level (debug, info, warn, error)
VITE_LOG_LEVEL=info