using Aihub.Server.Agent.Tool;
using Aihub.Server.Contexts;
using Aihub.Server.Llm;
using Aihub.Server.Options;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Agent.ChatAgents;

public class PrimaryAgent : IChatAgent
{
    private readonly ICurrentSessionIdAccessor _currentSessionIdAccessor;
    private readonly LlmClient _llmClient;
    private readonly AgentRunnerContext _agentRunnerContext;
    private readonly SubAgentConverter _subAgentConverter;
    private readonly ToolsDiscovery _toolsDiscovery;
    private readonly SubAgentAndToolCallTimesCounter _subAgentAndToolCallTimesCounter;
    public PrimaryAgent(
        SubAgentAndToolCallTimesCounter subAgentAndToolCallTimesCounter,
        ICurrentSessionIdAccessor currentSessionIdAccessor,
        SubAgentConverter subAgentConverter,
        LlmClient llmClient,
        AgentRunnerContext agentRunnerContext,
        ToolsDiscovery toolsDiscovery
    ){
        _currentSessionIdAccessor = currentSessionIdAccessor;
        _subAgentConverter = subAgentConverter;
        _llmClient = llmClient;
        _agentRunnerContext = agentRunnerContext;
        _toolsDiscovery = toolsDiscovery;
        _subAgentAndToolCallTimesCounter = subAgentAndToolCallTimesCounter;
    }

    public async Task RunAsync(CancellationToken cancellationToken)
    {
        while(true){

            // 需要判断是否上一个消息用户都已确认，如果确认了则需要执行对应的 ToolCall(Tool or SubAgent)
            var allToolExecuted = await _agentRunnerContext.ExecuteToolCallsIfAllConfirmed(cancellationToken);
            if(!allToolExecuted)
            {
                break;
            }

            _subAgentAndToolCallTimesCounter.Increment();

            var toolDescriptions = _toolsDiscovery.GetAvailableToolDescriptions(typeof(ExportTools), typeof(ChartJsTool));

            var subAgentOptions = await _agentRunnerContext.GetAvailableSubAgentOptionsAsync();
            var historyMessages = await _agentRunnerContext.GetLlmAwareHistoryMessagesAsync();
            var subAgentAsToolFunctions = AgentHelper.ToToolFunctions(subAgentOptions);
            var toolFunctions = AgentHelper.ToToolFunctions(toolDescriptions);

            var aggregateToolFunctions = subAgentAsToolFunctions.Concat(toolFunctions);

            var messageId = await _agentRunnerContext.SendBeginStreamingMessage();
            var response = _llmClient.CompleteChatStreamingAsync(
                messages: historyMessages,
                tools: aggregateToolFunctions,
                onChunk: (chunk, content) => _agentRunnerContext.SendStreamingMessage(messageId, chunk, content),
                cancellationToken: cancellationToken
            );

            var result = await response;
            var sessionId = _currentSessionIdAccessor.GetSessionId() ?? throw new Exception("SessionId is null");

            var subAgentAssistantMessageExtraToolCalls = _subAgentConverter.ConvertToolCalls(result.ToolCalls, subAgentOptions);
            var toolAssistantMessageExtraToolCalls = AgentHelper.ToAssistantMessageExtraToolCalls(result.ToolCalls, toolDescriptions);
            var aggregateAssistantMessageExtraToolCalls = subAgentAssistantMessageExtraToolCalls.Concat(toolAssistantMessageExtraToolCalls).ToList();

            if(aggregateAssistantMessageExtraToolCalls.Count != result.ToolCalls.Count){
                throw new Exception("Tool calls count mismatch");
            }

            await _agentRunnerContext.SendEndStreamingMessage(messageId, result.Content, aggregateAssistantMessageExtraToolCalls, result.InputTokenCount, result.OutputTokenCount);

            // 如果工具调用为空，则直接退出
            if(result.ToolCalls.Count == 0){
                break;
            }

            // 如果需要用户确认，则直接退出
            if(AgentHelper.NeedUserConfirm(aggregateAssistantMessageExtraToolCalls)){
                break;
            }
        }

    }
}
