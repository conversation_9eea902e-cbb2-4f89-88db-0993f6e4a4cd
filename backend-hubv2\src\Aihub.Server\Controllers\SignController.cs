using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Options;
using Aihub.Server.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Controllers;

[ApiController]
[Route("api/v1/sign")]
public class SignController : ControllerBase
{

    private class CaptchaCacheItem
    {
        public required string Captcha { get; set; }
        public required DateTime ExpiredAt { get; set; }
    }

    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly AihubOption _aihubOption;
    private static readonly ConcurrentDictionary<string, CaptchaCacheItem> _captchaCache = new();

    public SignController(IDbContextFactory<AihubDbContext> dbContextFactory, IOptions<AihubOption> options)
    {
        _dbContextFactory = dbContextFactory;
        _aihubOption = options.Value;
    }

    [HttpGet("captcha")]
    public ActionResult<CaptchaResponse> GetCaptcha()
    {
        var captcha = "0000"; // Captcha.Generate();
        var session = Guid.NewGuid();
        _captchaCache.TryAdd(session.ToString(), new CaptchaCacheItem { Captcha = captcha, ExpiredAt = DateTime.UtcNow.AddMinutes(5) });

        Task.Run(async () =>
        {
            await Task.Delay(TimeSpan.FromMinutes(5));
            _captchaCache.TryRemove(session.ToString(), out _);
        });

        return new CaptchaResponse { Session = session, CaptchaBase64 = captcha };
    }

    [HttpPost("in")]
    public async Task<ActionResult<SignInResponse>> SignIn([FromBody] SignInRequest request)
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        var tenant = await dbContext.Tenants.FirstOrDefaultAsync(t => t.Key == request.Tenant);
        if (tenant is null) throw new Exception("Invalid tenant");
        if (tenant.Disabled) throw new Exception("Tenant is disabled");

        var captcha = _captchaCache.TryGetValue(request.Session.ToString(), out var captchaItem) ? captchaItem.Captcha : throw new Exception("Invalid session");
        if (captcha != request.Captcha) throw new Exception("Invalid captcha");
        if (captchaItem.ExpiredAt < DateTime.UtcNow) throw new Exception("Captcha expired");
        _captchaCache.TryRemove(request.Session.ToString(), out _);

        var tenantUser = await dbContext.TenantUsers.FirstOrDefaultAsync(t => t.UniqueUserKey == request.Username && t.PasswordHash == request.Password && t.TenantId == tenant.Id);
        if (tenantUser is null) throw new Exception("Invalid username or password");

       
        Application? application = null;
        if (request.Application is not null)
        {
            application = await dbContext.Applications.FirstOrDefaultAsync(a => a.TenantId == tenant.Id && a.Key == request.Application);
            if (application is null) throw new Exception("Invalid application");
        }
        else
        {
            application = await dbContext.Applications.FirstOrDefaultAsync(a => a.TenantId == tenant.Id);
            if (application is null) throw new Exception("No default application");
        }

        var ticket = new Ticket
        {
            Id = Guid.CreateVersion7(),
            UserKey = tenantUser.UniqueUserKey,
            TicketCode = TicketHelper.CreateTicketCode(),
            ApplicationId = application.Id,
            TicketType = TicketType.OneTime,
            CreatedAt = DateTime.UtcNow,
            ExpiredAt = DateTime.UtcNow.AddMinutes(_aihubOption.TicketExpiredMinutes),
            TenantId = tenant.Id,
        };
        await dbContext.Tickets.AddAsync(ticket);
        await dbContext.SaveChangesAsync();

        return new SignInResponse { TicketCode = ticket.TicketCode };
    }
}

public class SignInRequest
{
    [Required]
    public required string Tenant { get; set; }

    public string? Application { get; set; }

    [Required]
    public required string Username { get; set; }

    [Required]
    public required string Password { get; set; }

    [Required]
    public required Guid Session { get; set; }

    [Required]
    public required string Captcha { get; set; }
}

public class SignInResponse
{
    public required string TicketCode { get; set; }
}

public class CaptchaResponse
{
    public required Guid Session { get; set; }

    public required string CaptchaBase64 { get; set; }
}