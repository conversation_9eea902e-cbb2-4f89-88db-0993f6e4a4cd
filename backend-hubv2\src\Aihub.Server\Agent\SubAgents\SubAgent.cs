
using Aihub.Server.Agent.Tool;
using Aihub.Server.Contexts;
using Aihub.Server.Llm;
using Aihub.Server.Models;
using Aihub.Server.Options;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Agent.SubAgents;


public abstract class SubAgent : ISubAgent
{
    protected readonly SubAgentRunnerContext SubAgentRunnerContext;
    private readonly LlmClient _llmClient;
    private readonly ToolsDiscovery _toolsDiscovery;
    private readonly SubAgentAndToolCallTimesCounter _subAgentAndToolCallTimesCounter;
    public SubAgent(
        SubAgentAndToolCallTimesCounter subAgentAndToolCallTimesCounter,
        ToolsDiscovery toolsDiscovery,
        SubAgentRunnerContext subAgentRunnerContext,
        LlmClient llmClient
    )
    {
        _subAgentAndToolCallTimesCounter = subAgentAndToolCallTimesCounter;
        _toolsDiscovery = toolsDiscovery;
        SubAgentRunnerContext = subAgentRunnerContext;
        _llmClient = llmClient;
    }

    public abstract IEnumerable<Type> GetToolTypes();

    public async Task<SubAgentResultType> RunAsync(CancellationToken cancellationToken)
    {
        while (true)
        {
            // 需要判断是否上一个消息用户都已确认，如果确认了则需要执行对应的 ToolCall(Tool or SubAgent)
            var allToolExecuted = await SubAgentRunnerContext.ExecuteToolCallsIfAllConfirmed(cancellationToken);
            if (!allToolExecuted)
            {
                return SubAgentResultType.WaitUserInteraction;
            }

            _subAgentAndToolCallTimesCounter.Increment();

            var toolDescriptions = _toolsDiscovery.GetAvailableToolDescriptions(GetToolTypes().ToArray());
            var historyMessages = await SubAgentRunnerContext.GetLlmAwareHistoryMessagesAsync();
            var toolFunctions = AgentHelper.ToToolFunctions(toolDescriptions);

            var result = await _llmClient.CompleteChatAsync(
                messages: historyMessages,
                tools: toolFunctions,
                cancellationToken: cancellationToken
            );

            var assistantMessageExtraToolCalls = AgentHelper.ToAssistantMessageExtraToolCalls(result.ToolCalls, toolDescriptions);
            await SubAgentRunnerContext.HandleBlockMessage(Guid.CreateVersion7(), result.Content, assistantMessageExtraToolCalls, result.InputTokenCount, result.OutputTokenCount);

            if(assistantMessageExtraToolCalls.Count != result.ToolCalls.Count){
                throw new Exception("Tool calls count mismatch");
            }

            // 如果需要用户确认，则直接退出
            if (AgentHelper.NeedUserConfirm(assistantMessageExtraToolCalls))
            {
                return SubAgentResultType.WaitUserInteraction;
            }

            if (await IsSubAgentJobDone(result.Content, assistantMessageExtraToolCalls, cancellationToken))
            {
                return SubAgentResultType.JobDone;
            }
        }
    }

    public virtual async Task<bool> IsSubAgentJobDone(string finalMessage, List<AssistantMessageExtraToolCall> assistantMessageExtraToolCalls, CancellationToken cancellationToken)
    {
        return await SubAgentRunnerContext.IsSubAgnetJobDone(finalMessage, assistantMessageExtraToolCalls, cancellationToken);
    }
}
