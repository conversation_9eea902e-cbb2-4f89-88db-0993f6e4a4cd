using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Scrutors;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Services;

public class SessionService : ITransientService {
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    public SessionService(IDbContextFactory<AihubDbContext> dbContextFactory){
        _dbContextFactory = dbContextFactory;
    }


    public async Task InitializeSessionIfNeeded(Guid sessionId, Func<Guid, Task<string>> promptProvider){
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        var session = await dbContext.Sessions.FirstOrDefaultAsync(x => x.Id == sessionId);
        if(session is null) throw new Exception("Session not found");
        
        var hasInitMessage = await dbContext.StoredMessages.AnyAsync(x => x.SessionId == sessionId);
        if(hasInitMessage) return;

        var prompt = await promptProvider(session.AgentOptionId);
        await dbContext.StoredMessages.AddAsync(new StoredMessage{
            Id = Guid.CreateVersion7(),
            SessionId = sessionId,
            Category = StoredMessageCategory.System,
            Content = prompt,
            CreatedAt = DateTime.UtcNow
        });

        await dbContext.SaveChangesAsync();

        
    }

    public async Task<Guid?> GetLatestChatSessionId(Guid clientIdentityId, Guid applicationId )
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var session = await dbContext.Sessions
            .Where(w => w.Type == SessionType.Chat)
            .Where(s => s.ClientIdentityId == clientIdentityId)
            .Where(s => s.ApplicationId == applicationId)
            .OrderByDescending(s => s.CreatedAt)
            .FirstOrDefaultAsync();

        return session?.Id;
    }

    public async Task<Guid> CreateChatSession(Guid clientIdentityId,Guid applicationId){
        
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        var application = await dbContext.Applications.FirstOrDefaultAsync(x => x.Id == applicationId);
        if (application is null) throw new Exception("Application not found");

        var agentOption = await dbContext.AgentOptions.FirstOrDefaultAsync(x => x.Id == application.AgentOptionId);
        if (agentOption is null) throw new Exception("AgentOption not found");

        var session = new Session
        {
            Id = Guid.CreateVersion7(),
            ClientIdentityId = clientIdentityId,
            Type = SessionType.Chat,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ApplicationId = applicationId,
            AgentOptionId = agentOption.Id,
        };
        dbContext.Sessions.Add(session);
        await dbContext.SaveChangesAsync();

        return session.Id;
    }

}