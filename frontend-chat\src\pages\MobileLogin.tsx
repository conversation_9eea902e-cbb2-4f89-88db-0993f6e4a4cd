import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { useToast } from "@/hooks/use-toast";
import { RefreshCw, Shield, ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";

const MobileLogin = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [captcha, setCaptcha] = useState("");
  const [captchaCode, setCaptchaCode] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [step, setStep] = useState<"email" | "verification">("email");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // 生成随机验证码
  const generateCaptcha = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 5; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setCaptchaCode(result);
  };

  useEffect(() => {
    generateCaptcha();
  }, []);

  // 处理邮箱登录
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: "请输入邮箱",
        variant: "destructive",
      });
      return;
    }

    if (!password) {
      toast({
        title: "请输入密码",
        variant: "destructive",
      });
      return;
    }

    if (captcha.toUpperCase() !== captchaCode) {
      toast({
        title: "验证码错误",
        variant: "destructive",
      });
      generateCaptcha();
      setCaptcha("");
      return;
    }

    setIsLoading(true);
    
    // 模拟发送验证码
    setTimeout(() => {
      setIsLoading(false);
      setStep("verification");
      toast({
        title: "验证码已发送",
        description: `验证码已发送到 ${email}`,
      });
    }, 1500);
  };

  // 处理验证码验证
  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (verificationCode.length !== 6) {
      toast({
        title: "请输入6位验证码",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    // 模拟验证成功后跳转到手机版聊天页面
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "登录成功",
        description: "欢迎回来！",
      });
      window.location.href = "/mobile";
    }, 1000);
  };

  const resetToEmail = () => {
    setStep("email");
    setVerificationCode("");
    setPassword("");
    generateCaptcha();
    setCaptcha("");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 px-3 py-6 safe-area-inset">
      {/* 顶部导航 */}
      <div className="flex items-center justify-between mb-6">
        <Link to="/" className="flex items-center gap-2 text-muted-foreground">
          <ArrowLeft className="h-5 w-5" />
          <span className="text-sm">返回</span>
        </Link>
      </div>
      
      <div className="flex flex-col justify-center min-h-[calc(100vh-120px)]">
        <Card className="w-full shadow-lg border-0 bg-card/50 backdrop-blur-sm">
          <CardHeader className="text-center space-y-3 pb-4">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center">
              <Shield className="h-8 w-8 text-primary" />
            </div>
            <CardTitle className="text-xl font-bold">
              {step === "email" ? "手机版登录" : "验证身份"}
            </CardTitle>
            <CardDescription className="text-sm">
              {step === "email" 
                ? "使用邮箱地址登录手机版" 
                : "请输入发送到您邮箱的验证码"
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4 pt-0">
            {step === "email" ? (
              <form onSubmit={handleEmailSubmit} className="space-y-4">
                <div className="space-y-1.5">
                  <Label htmlFor="email" className="text-sm font-medium">邮箱地址</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="请输入邮箱地址"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                    className="h-12 text-base"
                  />
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="password" className="text-sm font-medium">密码</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="请输入密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                    className="h-12 text-base"
                  />
                </div>
                
                <div className="space-y-1.5">
                  <Label htmlFor="captcha" className="text-sm font-medium">图片验证码</Label>
                  <div className="flex gap-2">
                    <Input
                      id="captcha"
                      placeholder="请输入验证码"
                      value={captcha}
                      onChange={(e) => setCaptcha(e.target.value)}
                      disabled={isLoading}
                      className="flex-1 h-12 text-base"
                    />
                    <div className="flex items-center gap-2">
                      <div className="bg-muted px-3 py-3 rounded-md border font-mono text-base tracking-wider select-none min-w-[80px] text-center">
                        {captchaCode}
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={generateCaptcha}
                        disabled={isLoading}
                        className="h-12 w-12"
                      >
                        <RefreshCw className="h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Button type="submit" className="w-full h-12 text-base font-medium mt-6" disabled={isLoading}>
                  {isLoading ? "登录中..." : "登录"}
                </Button>
              </form>
            ) : (
              <form onSubmit={handleVerificationSubmit} className="space-y-4">
                <div className="space-y-3">
                  <Label className="text-sm font-medium">邮箱验证码</Label>
                  <p className="text-sm text-muted-foreground">
                    验证码已发送至 {email}
                  </p>
                  <div className="flex justify-center py-2">
                    <InputOTP
                      maxLength={6}
                      value={verificationCode}
                      onChange={setVerificationCode}
                      disabled={isLoading}
                    >
                      <InputOTPGroup className="gap-2">
                        <InputOTPSlot index={0} className="h-12 w-12 text-lg" />
                        <InputOTPSlot index={1} className="h-12 w-12 text-lg" />
                        <InputOTPSlot index={2} className="h-12 w-12 text-lg" />
                        <InputOTPSlot index={3} className="h-12 w-12 text-lg" />
                        <InputOTPSlot index={4} className="h-12 w-12 text-lg" />
                        <InputOTPSlot index={5} className="h-12 w-12 text-lg" />
                      </InputOTPGroup>
                    </InputOTP>
                  </div>
                </div>
                
                <div className="flex gap-3 mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 h-12 text-base"
                    onClick={resetToEmail}
                    disabled={isLoading}
                  >
                    返回
                  </Button>
                  <Button 
                    type="submit" 
                    className="flex-1 h-12 text-base font-medium"
                    disabled={isLoading}
                  >
                    {isLoading ? "验证中..." : "登录"}
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MobileLogin;