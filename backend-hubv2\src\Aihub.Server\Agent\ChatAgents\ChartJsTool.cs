using Aihub.Server.Agent.Tool;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Schema;
using Newtonsoft.Json.Linq;

namespace Aihub.Server.Agent.ChatAgents;

public class ChartJsTool : ITool {

    private static readonly string _chartJsonSchemaStr = 
        """
        {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "Chart.js Configuration Schema",
        "type": "object",
        "required": ["type", "data"],
        "properties": {
            "type": {
            "type": "string",
            "enum": ["bar", "line", "pie", "doughnut", "radar", "polarArea"],
            "description": "图表类型"
            },
            "data": {
            "type": "object",
            "required": ["datasets"],
            "properties": {
                "labels": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "description": "图表标签"
                },
                "datasets": {
                "type": "array",
                "minItems": 1,
                "items": {
                    "type": "object",
                    "required": ["data"],
                    "properties": {
                    "label": {
                        "type": "string",
                        "description": "数据集标签"
                    },
                    "data": {
                        "type": "array",
                        "items": {
                        "type": "number"
                        },
                        "minItems": 1,
                        "description": "数据点"
                    },
                    "backgroundColor": {
                        "oneOf": [
                        {
                            "type": "string"
                        },
                        {
                            "type": "array",
                            "items": {
                            "type": "string"
                            }
                        }
                        ],
                        "description": "背景颜色"
                    },
                    "borderColor": {
                        "oneOf": [
                        {
                            "type": "string"
                        },
                        {
                            "type": "array",
                            "items": {
                            "type": "string"
                            }
                        }
                        ],
                        "description": "边框颜色"
                    },
                    "borderWidth": {
                        "type": "number",
                        "minimum": 0,
                        "description": "边框宽度"
                    },
                    "fill": {
                        "type": "boolean",
                        "description": "是否填充"
                    }
                    },
                    "additionalProperties": true
                }
                }
            }
            },
            "options": {
            "type": "object",
            "properties": {
                "responsive": {
                "type": "boolean",
                "description": "是否响应式"
                },
                "maintainAspectRatio": {
                "type": "boolean",
                "description": "是否保持纵横比"
                },
                "plugins": {
                "type": "object",
                "properties": {
                    "legend": {
                    "type": "object",
                    "properties": {
                        "position": {
                        "type": "string",
                        "enum": ["top", "bottom", "left", "right"],
                        "description": "图例位置"
                        }
                    }
                    }
                }
                },
                "scales": {
                "type": "object",
                "description": "坐标轴配置"
                }
            },
            "additionalProperties": true
            },
            "width": {
            "oneOf": [
                {
                "type": "number",
                "minimum": 100
                },
                {
                "type": "string",
                "enum": ["auto"]
                }
            ],
            "description": "图表宽度"
            },
            "height": {
            "type": "number",
            "minimum": 100,
            "description": "图表高度"
            },
            "minWidth": {
            "type": "number",
            "minimum": 100,
            "description": "最小宽度"
            },
            "maxWidth": {
            "type": "number",
            "minimum": 100,
            "description": "最大宽度"
            },
            "autoWidth": {
            "type": "boolean",
            "description": "是否自动调整宽度"
            }
        },
        "additionalProperties": false
        }
        """;
    private static readonly JSchema? _chartJsSchema;
    static ChartJsTool() {
        _chartJsSchema = JSchema.Parse(_chartJsonSchemaStr);
    }

    private readonly ILogger<ChartJsTool> _logger;

    public ChartJsTool(ILogger<ChartJsTool> logger) {
        _logger = logger;
    }

    [Tool("render_chart_js_to_markdown",
        """
        当用户需要展示图标时，请将对应的数据编写 chart.js 组件所需的 json 配置文件并传入本工具，本工具将输出用户可展示的 markdown 代码块。请你直接将输出的 markdown 代码块嵌入到返回内容中。
        """,
        NameForUser = "渲染图表",
        DescriptionForUser = "渲染图表",
        NeedConfirm = false,
        UserAware = true,
        Severity = ToolSeverity.Medium
    )]
    public Task<object> RenderChartJsToMarkdown(
        [Required]
        [Description(
            """
            一个段 JSON 字符串，表示 chart.js 的配置。
            示例如下：
            {
                "type": "bar",
                "data": {
                    "labels": [ ... ],
                    "datasets": [ ... ]
                },
                "options": { ... }
            }
            """)]
        string chartConfig) {
        
        // 验证JSON格式
        JObject? configJson;
        try {
            configJson = JObject.Parse(chartConfig);
        }
        catch (JsonException ex) {
            throw new ArgumentException($"无效的JSON格式: {ex.Message}", nameof(chartConfig));
        }

        if(configJson.Children().Count() == 0){
            throw new ArgumentException("chartJsJsonConfig 不能为空", nameof(chartConfig));
        }

        if(configJson.Children().Count() == 1){
            _logger.LogInformation("chartConfig 中只有一个字段，可以认为它是一个 root 对象");
            // 如果只有一个字段，可以认为它是一个 root 对象
            configJson = configJson.First!.Value<JObject>();
        }
        
        // 如果有Schema，进行验证
        if (_chartJsSchema != null) {
            var validationErrors = new List<string>();
            
            bool isValid = configJson.IsValid(_chartJsSchema, out IList<string> errorMessages);
            if (!isValid) {
                var errorDetails = string.Join("; ", errorMessages);
                throw new ArgumentException($"Chart.js 配置不符合Schema要求: {errorDetails}", nameof(chartConfig));
            }
        }
        
        // 基本字段验证
        ValidateBasicFields(configJson);
        
        return Task.FromResult<object>(
            $"""
            ```chart-js-json
            {configJson.ToString(Formatting.Indented)}
            ```
            """
        );
    }
    
    private static void ValidateBasicFields(JObject config) {
        // 验证必需字段
        if (config["type"] == null) {
            throw new ArgumentException("缺少必需字段 'type'");
        }
        
        if (config["data"] == null) {
            throw new ArgumentException("缺少必需字段 'data'");
        }
        
        // 验证图表类型
        var type = config["type"]?.ToString();
        var validTypes = new[] { "bar", "line", "pie", "doughnut", "radar", "polarArea" };
        if (!validTypes.Contains(type)) {
            throw new ArgumentException($"不支持的图表类型: {type}。支持的类型: {string.Join(", ", validTypes)}");
        }
        
        // 验证数据结构
        var data = config["data"] as JObject;
        if (data?["datasets"] == null) {
            throw new ArgumentException("data对象中缺少必需字段 'datasets'");
        }
        
        var datasets = data["datasets"] as JArray;
        if (datasets == null || datasets.Count == 0) {
            throw new ArgumentException("datasets必须是非空数组");
        }
        
        // 验证每个dataset
        foreach (var dataset in datasets.Cast<JObject>()) {
            if (dataset["data"] == null) {
                throw new ArgumentException("dataset中缺少必需字段 'data'");
            }
            
            var dataArray = dataset["data"] as JArray;
            if (dataArray == null || dataArray.Count == 0) {
                throw new ArgumentException("dataset的data字段必须是非空数组");
            }
            
            // 验证数据都是数字
            foreach (var dataPoint in dataArray) {
                if (dataPoint.Type != JTokenType.Integer && dataPoint.Type != JTokenType.Float) {
                    throw new ArgumentException("dataset的data数组中所有值必须是数字");
                }
            }
        }
    }

}