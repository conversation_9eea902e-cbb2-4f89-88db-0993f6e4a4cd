using System.Text.Json;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Models;
using Aihub.Server.Models.Dto;
using Aihub.Server.Options;
using Aihub.Server.Services;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Agent;

public class AgentRunnerContextHelper
{
    /// <summary>
    /// 如果所有工具调用都已确认，则执行工具调用并更新结果
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns>如果所有工具调用都已确认，则返回 true，否则返回 false</returns>
    public async static Task<bool> ExecuteToolCallsIfAllConfirmed(IDbContextFactory<AihubDbContext> dbContextFactory, Guid sessionId, ToolExecutor toolExecutor, CancellationToken cancellationToken)
    {
        // 获取当前会话的最新消息
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        var message = await dbContext.StoredMessages
            .Where(w => w.SessionId == sessionId)
            .OrderByDescending(o => o.CreatedAt)
            .FirstOrDefaultAsync();

        // 如果没有消息，直接返回 true
        if (message is null) return true;

        // 如果消息不是 Assistant 或 Tool 类型，直接返回 true
        if (message.Category != StoredMessageCategory.Assistant && message.Category != StoredMessageCategory.Tool)
        {
            return true;
        }

        // 如果是 Tool 类型消息，则找到对应的 Assistant 消息
        if (message.Category == StoredMessageCategory.Tool)
        {
            // 反序列化 ToolMessageExtra
            var toolMessageExtra = message.Extra is not null
                ? JsonSerializer.Deserialize<ToolMessageExtra>(message.Extra, StandardizedJsonOption.DefaultOptions)
                : null;
            if (toolMessageExtra is null) throw new Exception("Tool message extra not found");

            // 根据 AssistantMessageId 查找 Assistant 消息
            message = await dbContext.StoredMessages.FirstOrDefaultAsync(f => f.Id == toolMessageExtra.AssistantMessageId);
            if (message is null) throw new Exception("Assistant message not found");
        }

        // 反序列化 AssistantMessageExtra
        var assistantMessageExtra = message.Extra is not null
            ? JsonSerializer.Deserialize<AssistantMessageExtra>(message.Extra, StandardizedJsonOption.DefaultOptions)
            : null;

        if (assistantMessageExtra is null) throw new Exception("Assistant message extra not found");

        // 检查所有工具调用是否都已确认
        if (!assistantMessageExtra.ToolCalls.All(t => t.IsConfirm)) return false;
        
        // 执行工具调用
        await toolExecutor.ExecuteToolCalls(message.Id, cancellationToken);

        // 工具调用后，重新获取消息
        var assistantMessageAfterToolCalls = await dbContext.StoredMessages.AsNoTracking().FirstOrDefaultAsync(f => f.Id == message.Id);
        if (assistantMessageAfterToolCalls is null) throw new Exception("Assistant message not found");

        // 反序列化最新的 AssistantMessageExtra
        var assistantMessageExtraAfterToolCalls = assistantMessageAfterToolCalls.Extra is not null
            ? JsonSerializer.Deserialize<AssistantMessageExtra>(assistantMessageAfterToolCalls.Extra, StandardizedJsonOption.DefaultOptions)
            : null;
        if (assistantMessageExtraAfterToolCalls is null) throw new Exception("Assistant message extra not found");

        // 检查所有工具调用是否都已生成结果消息
        return assistantMessageExtraAfterToolCalls.ToolCalls.All(t => t.ToolResultMessageId is not null);
    }

    public static async Task<IEnumerable<AssistantMessageToolCallVo>> HandleBlockMessage(
        IDbContextFactory<AihubDbContext> dbContextFactory,
        ChatHubWrapper chatHubWrapper,
        string model,
        Guid sessionId,
        Guid messageId,
        string content,
        List<AssistantMessageExtraToolCall> toolCalls,
        int inputTokenCount,
        int outputTokenCount)
    {

        await using var dbContext = await dbContextFactory.CreateDbContextAsync();

        var assistantMessageExtraJson = toolCalls.Count > 0 ? JsonSerializer.Serialize(new AssistantMessageExtra
        {
            ResultMessageId = Guid.CreateVersion7(),
            ToolCalls = toolCalls
        }, StandardizedJsonOption.DefaultOptions) : null;


        var storedMessage = await dbContext.StoredMessages.FirstOrDefaultAsync(m => m.Id == messageId);
        if (storedMessage is null)
        {
            await dbContext.StoredMessages.AddAsync(new StoredMessage
            {
                Id = messageId,
                Content = content,
                Category = StoredMessageCategory.Assistant,
                CreatedAt = DateTime.UtcNow,
                SessionId = sessionId,
                Model = model,
                Extra = assistantMessageExtraJson,
                InputTokenCount = inputTokenCount,
                OutputTokenCount = outputTokenCount
            });
        }
        else
        {
            await dbContext.StoredMessages
                .Where(m => m.Id == messageId)
                .ExecuteUpdateAsync(u => u
                    .SetProperty(m => m.Content, content)
                    .SetProperty(m => m.Extra, assistantMessageExtraJson)
                    .SetProperty(m => m.InputTokenCount, inputTokenCount)
                    .SetProperty(m => m.OutputTokenCount, outputTokenCount)
            );
        }

        await dbContext.SaveChangesAsync();

        var toolCallsDto = toolCalls
            .Where(t => (t.NeedConfirm && !t.IsConfirm) || t.IsUserAware)
            .Select(t =>
            {
                return new AssistantMessageToolCallVo
                {
                    Id = t.Id,
                    ToolName = t.Name,
                    ToolDescription = t.Description,
                    NeedConfirm = t.NeedConfirm,
                    IsConfirm = t.IsConfirm,
                    Severity = t.Severity switch {
                        AssistantMessageExtraToolCallSeverity.Low => AssistantMessageToolCallSeverity.Low,
                        AssistantMessageExtraToolCallSeverity.Medium => AssistantMessageToolCallSeverity.Medium,
                        AssistantMessageExtraToolCallSeverity.High => AssistantMessageToolCallSeverity.High,
                        _ => throw new InvalidOperationException("Invalid severity")
                    }
                };
            }).ToList();

        return toolCallsDto;
    }
}