using Aihub.Server.Agent.Tool;
using Aihub.Server.Llm;
using Aihub.Server.Models;

namespace Aihub.Server.Agent;

public class AgentHelper
{

    public static IEnumerable<ToolFunction> ToToolFunctions(IEnumerable<SubAgentOptionWithDescription> subAgentOptions)
    {
        return subAgentOptions.Select(s => new ToolFunction
        {
            FunctionName = s.SubAgentDescription.Name,
            FunctionDescription = s.SubAgentDescription.Description,
            FunctionParameters = new ToolFunctionParameters
            {
                Parameters = new ToolFunctionParameter[]{
                    new ToolFunctionParameter{
                        ParameterName = s.SubAgentDescription.ParameterName,
                        ParameterDescription = s.SubAgentDescription.ParameterDescription,
                        ParameterType = ToolFunctionParameterType.String
                    }
                },
                Required = new string[] { s.SubAgentDescription.ParameterName },
            }
        });
    }

    public static IEnumerable<ToolFunction> ToToolFunctions(IEnumerable<ToolDescription> toolDescriptions)
    {
        return toolDescriptions.Select(t => new ToolFunction
        {
            FunctionName = t.Name,
            FunctionDescription = t.Description ?? string.Empty,
            FunctionParameters = new ToolFunctionParameters
            {
                Parameters = t.Parameters.Select(p => new ToolFunctionParameter
                {
                    ParameterName = p,
                    ParameterDescription = t.ParameterDescriptions[p],
                    ParameterType = t.ParameterTypes[p] switch
                    {
                        ToolDescriptionParameterType.String => ToolFunctionParameterType.String,
                        ToolDescriptionParameterType.Int => ToolFunctionParameterType.Int,
                        ToolDescriptionParameterType.Float => ToolFunctionParameterType.Float,
                        ToolDescriptionParameterType.Boolean => ToolFunctionParameterType.Boolean,
                        _ => throw new InvalidOperationException($"Unsupported parameter type: {t.ParameterTypes[p]}")
                    }
                }).ToArray(),
                Required = t.RequiredParameters
            }
        });
    }

    public static List<AssistantMessageExtraToolCall> ToAssistantMessageExtraToolCalls(List<ToolCall> toolCalls, IEnumerable<ToolDescription> toolDescriptions)
    {
        return toolCalls
            .Where(t => toolDescriptions.Any(td => td.Name == t.FunctionName))
            .Select(t =>
            {
                var toolDescription = toolDescriptions.FirstOrDefault(td => td.Name == t.FunctionName);
                if (toolDescription is null)
                {
                    throw new Exception($"Tool description not found for tool call: {t.FunctionName}");
                }

                return new AssistantMessageExtraToolCall
                {
                    Id = Guid.CreateVersion7(),
                    CallId = t.Id,
                    Type = AssistantMessageExtraToolCallType.ToolCall,
                    InternalId = toolDescription.ToolId,
                    CallName = t.FunctionName,
                    CallParameters = t.FunctionArguments,
                    Name = toolDescription.NameForUser ?? toolDescription.Name,
                    Description = toolDescription.DescriptionForUser ?? toolDescription.Description ?? string.Empty,
                    IsUserAware = toolDescription.UserAware,
                    NeedConfirm = toolDescription.NeedConfirm,
                    IsConfirm = !toolDescription.NeedConfirm,
                    Severity = toolDescription.Severity switch {
                        ToolSeverity.Low => AssistantMessageExtraToolCallSeverity.Low,
                        ToolSeverity.Medium => AssistantMessageExtraToolCallSeverity.Medium,
                        ToolSeverity.High => AssistantMessageExtraToolCallSeverity.High,
                        _ => throw new InvalidOperationException("Invalid severity")
                    }
                };
            }).ToList();
    }

    public static bool NeedUserConfirm(List<AssistantMessageExtraToolCall> assistantMessageExtraToolCalls)
    {
        return assistantMessageExtraToolCalls.Any() 
                && assistantMessageExtraToolCalls.Any(a => a.NeedConfirm && !a.IsConfirm);
    }

}
