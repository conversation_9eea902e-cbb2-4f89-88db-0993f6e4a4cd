using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites;

/// <summary>
/// 角色菜单关联实体，用于存储角色与菜单之间的关联关系
/// </summary>
public class RoleMenu {

    /// <summary>
    /// 角色菜单关联ID
    /// </summary>
    [Key]
    [Comment("角色菜单关联ID")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    [Comment("角色ID")]
    public required Guid RoleId { get; set; }

    /// <summary>
    /// 关联的角色实体导航属性
    /// </summary>
    [Comment("关联的角色实体导航属性")]
    public Role Role { get; set; } = null!;

    /// <summary>
    /// 菜单ID
    /// </summary>
    [Comment("菜单ID")]
    public required Guid MenuId { get; set; }

    /// <summary>
    /// 关联的菜单实体导航属性
    /// </summary>
    [Comment("关联的菜单实体导航属性")]
    public Menu Menu { get; set; } = null!;
}