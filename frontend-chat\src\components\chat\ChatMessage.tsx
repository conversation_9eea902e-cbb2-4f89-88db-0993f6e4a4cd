import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Check, X, Info, Settings, Clock, CheckCircle2 } from "lucide-react";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import aiAvatar from "@/assets/ai-avatar.png";
import userAvatar from "@/assets/user-avatar.png";
import type { Message, ToolCallDto, ChatMessageProps } from '@/types/chat';
import { markdownComponents } from '@/components/markdown/MarkdownComponents';

// Re-export for backward compatibility
export type { Message } from '@/types/chat';

export const ChatMessage = ({ message, profile, onToolCallAction }: ChatMessageProps) => {
  // Don't render anything if message has no content and no tool calls
  if ((!message.content || !message.content.trim()) && (!message.toolCalls || message.toolCalls.length === 0)) {
    return null;
  }

  const ToolCallCard = ({ toolCall }: { toolCall: ToolCallDto }) => {
    const getStatusIcon = () => {
      if (toolCall.isConfirm) {
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      } else if (toolCall.needConfirm) {
        return <Clock className="h-4 w-4 text-orange-500" />;
      } else {
        return <CheckCircle2 className="h-4 w-4 text-blue-500" />;
      }
    };

    const getStatusText = () => {
      if (toolCall.needConfirm && toolCall.isConfirm) {
        return '已确认';
      } else if (toolCall.needConfirm) {
        return '等待确认';
      } else {
        return '';
      }
    };

    const getSeverityStyles = () => {
      switch (toolCall.severity) {
        case 'High':
          return "border-l-red-500 bg-red-50 dark:bg-red-950/20";
        case 'Medium':
          return "border-l-orange-500 bg-orange-50 dark:bg-orange-950/20";
        case 'Low':
        default:
          return "border-l-green-500 bg-green-50 dark:bg-green-950/20";
      }
    };

    return (
      <Card className={cn(
        "border-l-4 transition-all duration-200",
        getSeverityStyles()
      )}>
        <CardHeader className="pb-1 pt-2 px-4 md:pb-1 md:pt-4">
          <div className="flex gap-2 items-start justify-between">
            <CardTitle className="text-sm flex items-start gap-2">
              <Settings className="h-4 w-4" />
              {toolCall.toolName}
            </CardTitle>
            <div className="flex items-center gap-1">
              {getStatusIcon()}
              <span className="text-xs text-muted-foreground">{getStatusText()}</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0 pb-2 md:pt-1 md:pb-4">
          <p className="text-sm text-muted-foreground mb-0">{toolCall.toolDescription}</p>
          
          { toolCall.needConfirm === true ? (!toolCall.isConfirm && onToolCallAction && (
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => onToolCallAction(toolCall.id, 'confirm')}
                className="flex-1"
              >
                <Check className="h-3 w-3 mr-1" />
                已确认
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onToolCallAction(toolCall.id, 'reject')}
                className="flex-1"
              >
                <X className="h-3 w-3 mr-1" />
                取消
              </Button>
            </div>
          )) : null}
        </CardContent>
      </Card>
    );
  };

  return (
    <div
      className={cn(
        "flex gap-2 sm:gap-3 py-1 sm:py-2 px-2 sm:px-4 lg:px-6 animate-fade-in message-enter",
        message.category === 'User' ? "justify-end" : "justify-start"
      )}
    >
      {message.category === 'Assistant' && (
        <Avatar className="hidden sm:flex h-8 w-8 shrink-0 mt-1">
          <AvatarImage 
            src={profile?.aiAvatar || aiAvatar} 
            alt="AI Assistant" 
          />
          <AvatarFallback className="bg-primary text-primary-foreground text-xs">
            {profile?.displayName?.substring(0, 2)?.toUpperCase() || 'AI'}
          </AvatarFallback>
        </Avatar>
      )}
      
      <div className={cn("max-w-[85%] sm:max-w-[80%] space-y-1 sm:space-y-2")}>
        {/* Regular message bubble */}
        {message.content && message.content.trim() !== '' && (
          <div
            className={cn(
              "rounded-2xl px-3 py-2 sm:px-4 sm:py-3 message-bubble",
              message.category === 'User'
                ? "bg-chat-user-bg text-chat-user-fg rounded-tr-sm"
                : message.category === 'Error'
                ? "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800 rounded-tl-sm"
                : "bg-chat-ai-bg text-chat-ai-fg rounded-tl-sm"
            )}
          >
            <div className="text-sm leading-relaxed">

              {message.category === 'Assistant' && message.content.trim() !== '' && (
                <div className="markdown-content">
                  <ReactMarkdown 
                    remarkPlugins={[remarkGfm]}
                    components={markdownComponents}
                  >
                    {message.content}
                  </ReactMarkdown>
                </div>
              )}

              {(message.category === 'User' || message.category === 'Error') && (
                <p className="whitespace-pre-wrap">{message.content}</p>
              )}
              
              {message.isStreaming && (
                <span className="inline-flex items-center ml-2">
                  <span className="animate-bounce w-1 h-1 bg-current rounded-full"></span>
                  <span className="animate-bounce w-1 h-1 bg-current rounded-full mx-1" style={{ animationDelay: '0.1s' }}></span>
                  <span className="animate-bounce w-1 h-1 bg-current rounded-full" style={{ animationDelay: '0.2s' }}></span>
                </span>
              )}
            </div>
            {message.category !== 'Error' && (
              <span className="text-xs opacity-70 mt-2 block">
                {message.timestamp.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </span>
            )}
          </div>
        )}

        {/* Tool calls */}
        {message.toolCalls && message.toolCalls.length > 0 && (
          <div className="space-y-2 min-w-[280px] sm:min-w-[400px]">
            {message.toolCalls.map((toolCall) => (
              <ToolCallCard key={toolCall.id} toolCall={toolCall} />
            ))}
          </div>
        )}
      </div>

      {message.category === 'User' && (
        <Avatar className="hidden sm:flex h-8 w-8 shrink-0 mt-1">
          <AvatarImage 
            src={profile?.userAvatar || userAvatar} 
            alt="User" 
          />
          <AvatarFallback className="bg-secondary text-secondary-foreground text-xs">
            You
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};