using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites.Tenanted;

/// <summary>
/// 应用程序实体，代表租户下的具体应用或服务
/// 一个租户可以拥有多个应用程序，实现细粒度的权限控制
/// </summary>
public class Application {
    /// <summary>
    /// 应用程序的唯一标识符
    /// </summary>
    [Key]
    [Comment("应用程序的唯一标识符")]
    public required Guid Id { get; set; }
    
    /// <summary>
    /// 应用程序标识符，用于标识不同的业务应用
    /// </summary>
    [Comment("应用程序标识符，用于标识不同的业务应用")]
    public required string Key { get; set; }

    /// <summary>
    /// 应用程序显示名称
    /// </summary>
    [Comment("应用程序显示名称")]
    public string? DisplayName { get; set; }
    
    /// <summary>
    /// 应用程序描述信息，说明应用的用途和功能
    /// </summary>
    [Comment("应用程序描述信息，说明应用的用途和功能")]
    public string? Description { get; set; }

    /// <summary>
    /// 应用程序图标
    /// </summary>
    [Comment("应用程序图标")]
    public string? Icon { get; set; }

    /// <summary>
    /// 应用程序退出URL
    /// </summary>
    [Comment("应用程序退出URL")]
    public string? SignOutUrl { get; set; }

    /// <summary>
    /// 应用程序退出URL
    /// </summary>
    [Comment("应用程序主题")]
    public string? Theme { get; set; }

    /// <summary>
    /// 应用程序AI头像
    /// </summary>
    [Comment("应用程序AI头像")]
    public string? AiAvatar { get; set; }

    /// <summary>
    /// 应用程序用户头像
    /// </summary>
    [Comment("应用程序用户头像")]
    public string? UserAvatar { get; set; }

    /// <summary>
    /// 推荐开始提示词
    /// </summary>
    [Comment("推荐开始提示词")]
    public List<string> RecommendedStartPrompts { get; set; } = new();

    /// <summary>
    /// 关联的AgentInfoId
    /// </summary>
    [ForeignKey(nameof(AgentOption))]
    [Comment("关联的AgentInfoId")]
    public Guid? AgentOptionId { get; set; }
    [Comment("关联的智能体实体导航属性")]
    public AgentOption? AgentOption { get; set; }

    /// <summary>
    /// 关联的租户ID，标识该应用程序属于哪个租户
    /// </summary>
    [ForeignKey(nameof(Tenant))]
    [Comment("关联的租户ID，标识该应用程序属于哪个租户")]
    public required Guid TenantId { get; set; }
    
    /// <summary>
    /// 关联的租户实体导航属性
    /// </summary>
    [Comment("关联的租户实体导航属性")]
    public Tenant Tenant { get; set; } = null!;
    
    /// <summary>
    /// 应用程序创建时间，记录应用注册到系统的时间
    /// </summary>
    [Comment("应用程序创建时间，记录应用注册到系统的时间")]
    public required DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
