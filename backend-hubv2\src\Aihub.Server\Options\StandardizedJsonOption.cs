using System.Text.Json;
using System.Text.Json.Serialization;

namespace Aihub.Server.Options;

public class StandardizedJsonOption {
    public static JsonSerializerOptions DefaultOptions = new JsonSerializerOptions{
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true,
        Converters = {
            new JsonStringEnumConverter()
        },
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
    };
}
