using System.Reflection;
using System.Text.Json;
using System.Text.Json.Nodes;
using Aihub.Server.Agent.Tool;
using Aihub.Server.Contexts;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Models;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Agent;

public class ToolExecutor : ITransientService
{

    private readonly ToolsDiscovery _toolsDiscovery;
    private readonly AgentPromptAccessor _agentPromptAccessor;
    private readonly ICurrentClientIdentityAccessor _currentClientIdentityAccessor;
    private readonly SubAgentsDiscovery _subAgentsDiscovery;
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ToolExecutor> _logger;
    public ToolExecutor(
        ToolsDiscovery toolsDiscovery,
        SubAgentsDiscovery subAgentsDiscovery,
        AgentPromptAccessor agentPromptAccessor,
        ICurrentClientIdentityAccessor currentClientIdentityAccessor,
        IDbContextFactory<AihubDbContext> dbContextFactory,
        IServiceProvider serviceProvider,
        ILogger<ToolExecutor> logger
    )
    {
        _toolsDiscovery = toolsDiscovery;
        _agentPromptAccessor = agentPromptAccessor;
        _currentClientIdentityAccessor = currentClientIdentityAccessor;
        _serviceProvider = serviceProvider;
        _dbContextFactory = dbContextFactory;
        _subAgentsDiscovery = subAgentsDiscovery;
        _logger = logger;
    }

    public async Task ExecuteToolCalls(Guid messageId, CancellationToken cancellationToken)
    {

        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var assistantMessage = await dbContext.StoredMessages.FirstOrDefaultAsync(f => f.Id == messageId);

        if (assistantMessage is null || assistantMessage.Category != StoredMessageCategory.Assistant || assistantMessage.Extra is null) throw new Exception("Assistant message not found");
        var assistantMessageExtra = JsonSerializer.Deserialize<AssistantMessageExtra>(assistantMessage.Extra, StandardizedJsonOption.DefaultOptions);

        if (assistantMessageExtra is null) throw new Exception("Assistant message extra not found");
        var toolCalls = assistantMessageExtra.ToolCalls;

        if (toolCalls.Any(a => !a.IsConfirm)) throw new Exception("Tool call is not confirmed");

        List<Task> tasks = new List<Task>();
        foreach (var toolCall in toolCalls)
        {
            if(toolCall.Type == AssistantMessageExtraToolCallType.ToolCall){
                tasks.Add(ExecuteTool(messageId, toolCall, cancellationToken));
            }else{
                tasks.Add(ExecuteSugAgentAsync(messageId, toolCall, cancellationToken));
            }
        }
        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 执行 SubAgent（子代理）调用的异步方法
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="assistantMessageExtraToolCall">工具调用的详细信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ExecuteSugAgentAsync(Guid messageId, AssistantMessageExtraToolCall assistantMessageExtraToolCall, CancellationToken cancellationToken)
    {
        // 检查当前客户端身份ID是否存在
        if (_currentClientIdentityAccessor.GetClientIdentityId() is null)
            throw new Exception("ClientIdentityId is null");

        // 校验工具调用类型是否为 SubAgentCall
        if (assistantMessageExtraToolCall.Type != AssistantMessageExtraToolCallType.SubAgentCall)
            throw new Exception("Tool call type is not sub agent call");

        // 校验 InternalId 是否存在
        if (assistantMessageExtraToolCall.InternalId is null)
            throw new Exception("InternalId is null");

        // 尝试将 InternalId 转换为 Guid
        Guid subAgentOptionId = Guid.Empty;
        if (!Guid.TryParse(assistantMessageExtraToolCall.InternalId, out subAgentOptionId))
            throw new Exception("InternalId is not a valid guid");

        // 创建数据库上下文
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        // 如果 SessionId 为空，说明是首次调用，需要新建 Session
        if (assistantMessageExtraToolCall.SessionId is null)
        {
            // 查询消息
            var message = await dbContext.StoredMessages.FirstOrDefaultAsync(f => f.Id == messageId);
            if (message is null) throw new Exception("Message not found");

            // 反序列化 Extra 字段
            var extra = message?.Extra is not null ? JsonSerializer.Deserialize<AssistantMessageExtra>(message.Extra, StandardizedJsonOption.DefaultOptions) : null;
            if (extra is null) throw new Exception("Extra is null");
            if (extra.ToolCalls.Count == 0) throw new Exception("Tool calls is empty");

            // 查找对应的 ToolCall
            var toolcall = extra.ToolCalls.FirstOrDefault(f => f.Id == assistantMessageExtraToolCall.Id);
            if (toolcall is null) throw new Exception("Tool call not found");

            // 创建新的 SubAgent 会话
            var subAgentSession = new Session
            {
                Id = Guid.CreateVersion7(),
                Type = SessionType.SubAgent,
                ClientIdentityId = _currentClientIdentityAccessor.GetClientIdentityId()!.Value,
                AgentOptionId = subAgentOptionId,
                ParentMessageId = messageId,
                ParentSessionId = message!.SessionId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // 添加新会话到数据库
            await dbContext.Sessions.AddAsync(subAgentSession);

            // 获取 SubAgent 的 Prompt
            var prompt = await _agentPromptAccessor.GetPromptAsync(subAgentOptionId);
            if (prompt is null) throw new Exception("Prompt is null");

            // 保存系统消息
            await dbContext.StoredMessages.AddAsync(new StoredMessage
            {
                Id = Guid.CreateVersion7(),
                SessionId = subAgentSession.Id,
                Category = StoredMessageCategory.System,
                Content = prompt,
                CreatedAt = DateTime.UtcNow,
                Extra = null,
            });

            // 获取 SubAgent 的参数
            var subAgentOption = await dbContext.AgentOptions.FirstOrDefaultAsync(f => f.Id == subAgentOptionId);
            var subAgentDescription = _subAgentsDiscovery.DiscoverTools().FirstOrDefault(f => f.Key == subAgentOption!.Key);
            JsonNode? parametersJson = null;
            string? parameterValue = null;

            try
            {
                parametersJson = JsonObject.Parse(assistantMessageExtraToolCall.CallParameters);
                parameterValue = parametersJson?[subAgentDescription!.ParameterName]?.ToString() ?? throw new Exception("Parameter value is null");
            }
            catch(Exception e)
            {
                _logger.LogError(e, "Error in ToolExecutor.ExecuteSugAgentAsync");
                var exceptionMessage =  e is JsonException ? "Parameter parse failed" : e.Message;
                await UpdateToolCallResult(messageId, assistantMessageExtraToolCall, exceptionMessage, false);
                return;
            }

            // 保存用户消息
            await dbContext.StoredMessages.AddAsync(new StoredMessage
            {
                Id = Guid.CreateVersion7(),
                SessionId = subAgentSession.Id,
                Category = StoredMessageCategory.User,
                Content = parameterValue,
                CreatedAt = DateTime.UtcNow,
                Extra = null,
            });

            // 关联 ToolCall 和 ExtraToolCall 的 SessionId
            toolcall.SessionId = subAgentSession.Id;
            assistantMessageExtraToolCall.SessionId = subAgentSession.Id;

            // 更新消息的 Extra 字段
            var extraJson = JsonSerializer.Serialize(extra, StandardizedJsonOption.DefaultOptions);
            await dbContext.StoredMessages
                    .Where(w => w.Id == messageId)
                    .ExecuteUpdateAsync(u => u.SetProperty(w => w.Extra, extraJson));

            // 保存更改
            await dbContext.SaveChangesAsync();
        }

        // 获取 SubAgentRunner 并执行子代理
        var subAgentRunner = _serviceProvider.GetRequiredService<SubAgentRunner>();
        await subAgentRunner.RunAsync(subAgentOptionId, assistantMessageExtraToolCall.SessionId!.Value, assistantMessageExtraToolCall.Id, cancellationToken);

    }

    private async Task ExecuteTool(Guid messageId, AssistantMessageExtraToolCall assistantMessageExtraToolCall, CancellationToken cancellationToken)
    {
        object? result = null;
        bool isSuccess = false;

        try
        {
            List<object?> convertedParameters = new List<object?>();

            var parameters = JsonSerializer.Deserialize<Dictionary<string, object>>(assistantMessageExtraToolCall.CallParameters);
            var toolDescription = _toolsDiscovery.DiscoverTools().FirstOrDefault(f => f.ToolId == assistantMessageExtraToolCall.InternalId);
            if (toolDescription is null) throw new Exception($"Tool {assistantMessageExtraToolCall.InternalId} not found");

            foreach (var parameter in parameters!)
            {
                var parameterType = toolDescription.ParameterTypes[parameter.Key];
                object? convertedParameter = null;

                if (parameter.Value is not null)
                {
                    switch (parameterType)
                    {
                        case ToolDescriptionParameterType.String:
                            convertedParameter = parameter.Value.ToString();
                            break;
                        case ToolDescriptionParameterType.Int:
                            convertedParameter = int.Parse(parameter.Value.ToString()!);
                            break;
                        case ToolDescriptionParameterType.Float:
                            convertedParameter = float.Parse(parameter.Value.ToString()!);
                            break;
                        case ToolDescriptionParameterType.Boolean:
                            convertedParameter = bool.Parse(parameter.Value.ToString()!);
                            break;
                        default:
                            throw new Exception($"Unsupported parameter type: {parameterType}");
                    }
                    ;
                }

                convertedParameters.Add(convertedParameter);
            }



            var tool = _serviceProvider.GetRequiredService(toolDescription.ToolType) as ITool;
            if (tool is null) throw new Exception($"Tool {assistantMessageExtraToolCall.InternalId} not found");

            var task = toolDescription.ToolMethod.Invoke(tool, convertedParameters.ToArray())! as Task<object>
                    ?? throw new Exception($"Tool {assistantMessageExtraToolCall.InternalId} returned a non-Task result");

            result = await task;
            isSuccess = true;
        }
        catch (TargetInvocationException e)
        {
            _logger.LogError(e, "Error in ToolExecutor.ExecuteTool");
            result = e.InnerException?.Message ?? e.Message;
            isSuccess = false;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error in ToolExecutor.ExecuteTool");
            result = e.Message;
            isSuccess = false;
        }

        await UpdateToolCallResult(messageId, assistantMessageExtraToolCall, result, isSuccess);

    }

    public async Task UpdateToolCallResult(Guid messageId, AssistantMessageExtraToolCall assistantMessageExtraToolCall, object? result, bool isSuccess)
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        using var transaction = await dbContext.Database.BeginTransactionAsync();

        try
        {

            var assistantMessage = await dbContext.StoredMessages.FirstOrDefaultAsync(f => f.Id == messageId);
            if (assistantMessage is null || assistantMessage.Category != StoredMessageCategory.Assistant || assistantMessage.Extra is null) throw new Exception("Assistant message not found");

            var assistantMessageExtra = JsonSerializer.Deserialize<AssistantMessageExtra>(assistantMessage.Extra, StandardizedJsonOption.DefaultOptions);
            if (assistantMessageExtra is null) throw new Exception("Assistant message extra not found");

            var toolCall = assistantMessageExtra.ToolCalls.FirstOrDefault(f => f.Id == assistantMessageExtraToolCall.Id);
            if (toolCall is null) throw new Exception("Tool call not found");

            var toolMessageExtraToolCallResult = new ToolMessageExtra
            {
                AssistantMessageId = messageId,
                CallId = assistantMessageExtraToolCall.CallId,
                InternalId = assistantMessageExtraToolCall.InternalId,
                IsSuccess = isSuccess,
            };

            var toolMessage = new StoredMessage
            {
                Id = Guid.CreateVersion7(),
                Category = StoredMessageCategory.Tool,
                SessionId = assistantMessage.SessionId,
                Content = result is string ? result.ToString() : JsonSerializer.Serialize(result, StandardizedJsonOption.DefaultOptions),
                CreatedAt = DateTime.UtcNow,
                Extra = JsonSerializer.Serialize(toolMessageExtraToolCallResult, StandardizedJsonOption.DefaultOptions),
            };

            toolCall.ToolResultMessageId = toolMessage.Id;

            var extraJson = JsonSerializer.Serialize(assistantMessageExtra, StandardizedJsonOption.DefaultOptions);

            await dbContext.StoredMessages.AddAsync(toolMessage);
            await dbContext.StoredMessages.Where(w => w.Id == messageId)
                .ExecuteUpdateAsync(u => u.SetProperty(w => w.Extra, extraJson));

            await dbContext.SaveChangesAsync();
            await transaction.CommitAsync();
            
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }


    }


}