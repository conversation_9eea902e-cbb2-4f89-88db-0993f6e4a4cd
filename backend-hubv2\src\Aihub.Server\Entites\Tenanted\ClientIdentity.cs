using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites.Tenanted;

/// <summary>
/// 客户端身份实体，用于标识和跟踪访问系统的客户端信息
/// 包含客户端的基本信息如浏览器标识、IP地址等，用于安全和审计目的
/// </summary>
public class ClientIdentity {
    /// <summary>
    /// 客户端身份的唯一标识符
    /// </summary>
    [Key]
    [Comment("客户端身份的唯一标识符")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 客户在租户中的唯一标识符
    /// </summary>
    [Required]
    [MaxLength(256)]
    [Comment("客户在租户中的唯一标识符")]
    public required string UniqueUserKey {get;set;}
    
    /// <summary>
    /// 客户端的用户代理字符串，包含浏览器和操作系统信息
    /// </summary>
    [Comment("客户端的用户代理字符串，包含浏览器和操作系统信息")]
    public string? UserAgent { get; set; }
    
    /// <summary>
    /// 客户端的IP地址，用于地理位置和安全分析
    /// </summary>
    [Comment("客户端的IP地址，用于地理位置和安全分析")]
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// HTTP请求的来源页面URL，用于跟踪用户访问来源
    /// </summary>
    [Comment("HTTP请求的来源页面URL，用于跟踪用户访问来源")]
    public string? Referer { get; set; }

    /// <summary>
    /// 关联的租户ID，标识该客户端属于哪个租户
    /// </summary>
    [ForeignKey(nameof(Tenant))]
    [Comment("关联的租户ID，标识该客户端属于哪个租户")]
    public required Guid TenantId { get; set; }
    
    /// <summary>
    /// 关联的租户实体导航属性
    /// </summary>
    [Comment("关联的租户实体导航属性")]
    public Tenant Tenant { get; set; } = null!;

    /// <summary>
    /// 客户端身份创建时间，用于审计和数据分析
    /// </summary>
    [Comment("客户端身份创建时间，用于审计和数据分析")]
    public required DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 该客户端身份下的所有会话集合，一个客户端可以有多个会话
    /// </summary>
    [Comment("该客户端身份下的所有会话集合，一个客户端可以有多个会话")]
    public ICollection<Session> ChatSessions { get; set; } = new List<Session>();
}