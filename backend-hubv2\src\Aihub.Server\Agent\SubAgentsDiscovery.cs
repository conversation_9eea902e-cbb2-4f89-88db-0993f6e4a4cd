using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Aihub.Server.Models;
using Aihub.Server.Scrutors;

namespace Aihub.Server.Agent;

public class SubAgentsDiscovery : ISingletonService
{
    private Lazy<Dictionary<string, SubAgentDescription>> _tools = new Lazy<Dictionary<string, SubAgentDescription>>(() => {
        var subAgents = new Dictionary<string, SubAgentDescription>();

        var subAgentTypes = typeof(ISubAgent).Assembly.GetTypes()
            .Where(t => t.IsPublic) // only public types
            .Where(t => !t.IsAbstract) // only non-abstract types
            .Where(t => !t.IsInterface) // only non-interface types
            .Where(t => t.GetCustomAttribute<SubAgentAttribute>() != null) // only types that have SubAgentAttribute
            .Where(t => typeof(ISubAgent).IsAssignableFrom(t)); // only types that implement ISubAgent

        foreach (var subAgentType in subAgentTypes)
        {
            var subAgentAttribute = subAgentType.GetCustomAttribute<SubAgentAttribute>();

            if(subAgentAttribute is null) continue;

            var toolInfo = new SubAgentDescription{
                Key = subAgentAttribute.Key,
                Name = subAgentAttribute.Name,
                Description = subAgentAttribute.Description,
                DisplayName = subAgentAttribute.DisplayName,
                ParameterName = subAgentAttribute.ParameterName,
                ParameterDescription = subAgentAttribute.ParameterDescription,
                ToolType = subAgentType,
                UserAware = true,
                Severity = subAgentAttribute.Severity switch {
                    SubAgentSeverity.Low => subAgentAttribute.Severity,
                    SubAgentSeverity.Medium => subAgentAttribute.Severity,
                    SubAgentSeverity.High => subAgentAttribute.Severity,
                    _ => throw new InvalidOperationException("Invalid severity")
                }
            };

            subAgents.Add(toolInfo.Name, toolInfo);
        }

        return subAgents;
    });

    public IEnumerable<SubAgentDescription> DiscoverTools() {
        return _tools.Value.Values;
    }
}
