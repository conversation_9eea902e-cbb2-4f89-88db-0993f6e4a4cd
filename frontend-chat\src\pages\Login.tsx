import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { useToast } from "@/hooks/use-toast";
import { useTheme } from "@/hooks/use-theme";
import { RefreshCw, Shield, Moon, Sun } from "lucide-react";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [captcha, setCaptcha] = useState("");
  const [captchaCode, setCaptchaCode] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [step, setStep] = useState<"email" | "verification">("email");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { theme, toggleTheme } = useTheme();

  // 生成随机验证码
  const generateCaptcha = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 5; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setCaptchaCode(result);
  };

  useEffect(() => {
    generateCaptcha();
  }, []);

  // 处理邮箱登录
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: "请输入邮箱",
        variant: "destructive",
      });
      return;
    }

    if (!password) {
      toast({
        title: "请输入密码",
        variant: "destructive",
      });
      return;
    }

    if (captcha.toUpperCase() !== captchaCode) {
      toast({
        title: "验证码错误",
        variant: "destructive",
      });
      generateCaptcha();
      setCaptcha("");
      return;
    }

    setIsLoading(true);
    
    // 模拟发送验证码
    setTimeout(() => {
      setIsLoading(false);
      setStep("verification");
      toast({
        title: "验证码已发送",
        description: `验证码已发送到 ${email}`,
      });
    }, 1500);
  };

  // 处理验证码验证
  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (verificationCode.length !== 6) {
      toast({
        title: "请输入6位验证码",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    // 模拟验证
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "登录成功",
        description: "欢迎回来！",
      });
      // 这里可以处理登录成功后的逻辑
    }, 1000);
  };

  const resetToEmail = () => {
    setStep("email");
    setVerificationCode("");
    setPassword("");
    generateCaptcha();
    setCaptcha("");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20 px-4 relative">
      {/* 主题切换按钮 */}
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleTheme}
        className="absolute top-4 right-4 h-10 w-10 rounded-full theme-toggle"
        title={theme === "light" ? "切换到暗色主题" : "切换到亮色主题"}
      >
        {theme === "light" ? (
          <Moon className="h-5 w-5" />
        ) : (
          <Sun className="h-5 w-5" />
        )}
      </Button>
      
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center space-y-2">
          <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <Shield className="h-6 w-6 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold">
            {step === "email" ? "登录" : "验证身份"}
          </CardTitle>
          <CardDescription>
            {step === "email" 
              ? "输入您的邮箱地址以继续" 
              : "请输入发送到您邮箱的验证码"
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {step === "email" ? (
            <form onSubmit={handleEmailSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="请输入邮箱地址"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="请输入密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="captcha">图片验证码</Label>
                <div className="flex gap-2">
                  <Input
                    id="captcha"
                    placeholder="请输入验证码"
                    value={captcha}
                    onChange={(e) => setCaptcha(e.target.value)}
                    disabled={isLoading}
                    className="flex-1"
                  />
                  <div className="flex items-center gap-2">
                    <div className="bg-muted px-3 py-2 rounded-md border font-mono text-lg tracking-wider select-none">
                      {captchaCode}
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={generateCaptcha}
                      disabled={isLoading}
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
              
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "发送中..." : "登录"}
              </Button>
            </form>
          ) : (
            <form onSubmit={handleVerificationSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label>邮箱验证码</Label>
                <p className="text-sm text-muted-foreground">
                  验证码已发送至 {email}
                </p>
                <div className="flex justify-center">
                  <InputOTP
                    maxLength={6}
                    value={verificationCode}
                    onChange={setVerificationCode}
                    disabled={isLoading}
                  >
                    <InputOTPGroup>
                      <InputOTPSlot index={0} />
                      <InputOTPSlot index={1} />
                      <InputOTPSlot index={2} />
                      <InputOTPSlot index={3} />
                      <InputOTPSlot index={4} />
                      <InputOTPSlot index={5} />
                    </InputOTPGroup>
                  </InputOTP>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  onClick={resetToEmail}
                  disabled={isLoading}
                >
                  返回
                </Button>
                <Button 
                  type="submit" 
                  className="flex-1"
                  disabled={isLoading}
                >
                  {isLoading ? "验证中..." : "登录"}
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Login;