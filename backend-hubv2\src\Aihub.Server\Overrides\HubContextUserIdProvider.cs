using Aihub.Server.Scrutors;
using Microsoft.AspNetCore.SignalR;

namespace Aihub.Server.Overrides;

public class HubContextUserIdProvider : IUserIdProvider, ISingletonService
{
    public string? GetUserId(HubConnectionContext connection)
    {
        var httpContext = connection.GetHttpContext();
        var userId = httpContext?.Request.Query["ticket"].ToString();
        return string.IsNullOrWhiteSpace(userId) ? null : userId;
    }
}