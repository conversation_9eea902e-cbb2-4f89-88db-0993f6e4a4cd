import { useState, useEffect } from "react";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { SignalRProvider } from '@/contexts/SignalRContext';
import { ChatContainer } from "@/components/chat/ChatContainer.improved";
import { StorageFactory } from '@/lib/services/storage.service';
import { createErrorHandler } from '@/lib/error-handler.improved';
import { useToast } from "@/hooks/use-toast";
import { APP_CONFIG } from '@/config/constants';

/**
 * Improved main application component with better configuration and error handling
 */
const App: React.FC = () => {
  const [storageService] = useState(() => StorageFactory.createStorage('local'));
  const [errorHandler] = useState(() => createErrorHandler({
    developmentMode: process.env.NODE_ENV === 'development',
    defaultUserMessage: 'Something went wrong. Please try again.',
    // Add custom error mappings if needed
    messageMapping: {
      'signalr': 'Connection to AI service failed. Please refresh the page.',
      'websocket': 'Real-time connection lost. Please refresh the page.',
    },
    contextMapping: {
      'SignalR Connection': 'Unable to connect to the AI service. Please check your connection and try again.',
      'Message Send': 'Failed to send your message. Please try again.',
    }
  }));

  const { toast } = useToast();
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');

  /**
   * Handle connection state changes
   */
  const handleConnectionStateChange = (connected: boolean) => {
    const newState = connected ? 'connected' : 'disconnected';
    setConnectionState(newState);
    
    // Show toast notification for connection state changes
    if (connected) {
      toast({
        title: "Connected",
        description: "Successfully connected to AI service",
        variant: "default",
      });
    } else {
      toast({
        title: "Disconnected", 
        description: "Lost connection to AI service. Trying to reconnect...",
        variant: "destructive",
      });
    }
  };

  /**
   * Handle errors with toast notifications
   */
  const handleError = (message: string) => {
    toast({
      title: "Error",
      description: message,
      variant: "destructive",
    });
  };

  // Configure SignalR options
  const signalROptions = {
    url: APP_CONFIG.SIGNALR.DEFAULT_URL,
    ticket: APP_CONFIG.SIGNALR.DEFAULT_TICKET,
    storageService,
    onConnectionStateChange: handleConnectionStateChange,
    onError: handleError,
  };

  useEffect(() => {
    // Log application start in development
    if (process.env.NODE_ENV === 'development') {
      console.info('AI Chat Application started', {
        signalRUrl: signalROptions.url,
        storageAvailable: storageService.isAvailable(),
      });
    }
  }, [signalROptions.url, storageService]);

  return (
    <ThemeProvider defaultTheme="system" storageKey={APP_CONFIG.STORAGE_KEYS.THEME}>
      <SignalRProvider options={signalROptions}>
        <div className="flex h-screen bg-background">
          {/* Connection status indicator */}
          <div className="fixed top-4 right-4 z-50">
            {connectionState === 'connecting' && (
              <div className="flex items-center space-x-2 bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg px-3 py-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-yellow-800 dark:text-yellow-200">Connecting...</span>
              </div>
            )}
            {connectionState === 'disconnected' && (
              <div className="flex items-center space-x-2 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg px-3 py-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm text-red-800 dark:text-red-200">Disconnected</span>
              </div>
            )}
          </div>

          {/* Main chat interface */}
          <div className="flex-1 flex flex-col">
            <ChatContainer />
          </div>
        </div>
        <Toaster />
      </SignalRProvider>
    </ThemeProvider>
  );
};

export default App;