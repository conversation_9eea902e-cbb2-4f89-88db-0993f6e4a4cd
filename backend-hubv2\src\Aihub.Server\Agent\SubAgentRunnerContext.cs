using System.Text.Json;
using Aihub.Server.Agent.Tool;
using Aihub.Server.Contexts;
using Aihub.Server.Converters;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Models;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using Aihub.Server.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Agent;

public class SubAgentRunnerContext : ITransientService
{
    private readonly ICurrentSessionIdAccessor _currentSessionIdAccessor;
    private readonly ICurrentSubAgentSessionIdAccessor _currentSubAgentSessionIdAccessor;
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly ChatHubWrapper _chatHubWrapper;
    private readonly ToolsDiscovery _toolsDiscovery;
    private readonly ToolExecutor _toolExecutor;
    private readonly AihubOption _aiHubOption;
    public SubAgentRunnerContext(
        ICurrentSessionIdAccessor currentSessionIdAccessor,
        ICurrentSubAgentSessionIdAccessor currentSubAgentSessionIdAccessor, 
        ToolsDiscovery toolsDiscovery,
        IDbContextFactory<AihubDbContext> dbContextFactory,
        ChatHubWrapper chatHubWrapper,
        ToolExecutor toolExecutor,
        IOptions<AihubOption> aiHubOption
    ){
        _currentSessionIdAccessor = currentSessionIdAccessor;
        _currentSubAgentSessionIdAccessor = currentSubAgentSessionIdAccessor;
        _toolsDiscovery = toolsDiscovery;
        _dbContextFactory = dbContextFactory;
        _chatHubWrapper = chatHubWrapper;
        _toolExecutor = toolExecutor;
        _aiHubOption = aiHubOption.Value;
    }

    public async Task<List<Message>> GetLlmAwareHistoryMessagesAsync()
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        var messages = await dbContext.StoredMessages
                .Where(m => m.SessionId == _currentSubAgentSessionIdAccessor.GetSubAgentSessionId())
                .OrderBy(m => m.CreatedAt)
                .Select(m => StoredToMessageConverter.ConvertToMessage(m))
                .ToListAsync();

        return messages.Where(w => w is UserMessage || w is AssistantMessage || w is SystemMessage || w is ToolMessage).ToList();
    }

    public async Task HandleBlockMessage(Guid messageId, string content, List<AssistantMessageExtraToolCall> toolCalls, int inputTokenCount, int outputTokenCount)
    {
        var sessionId = _currentSubAgentSessionIdAccessor.GetSubAgentSessionId() ?? throw new Exception("SessionId is null");
        var toolCallsVo = await AgentRunnerContextHelper.HandleBlockMessage(_dbContextFactory, _chatHubWrapper, _aiHubOption.DefaultModel, sessionId, messageId, content, toolCalls,inputTokenCount, outputTokenCount);

        // 如果含有任何的 toolCall 内容
        if (toolCallsVo.Any())
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
            var subAgentAssistantMessage = new StoredMessage{
                Id = Guid.CreateVersion7(),
                SessionId = _currentSessionIdAccessor.GetSessionId() ?? throw new Exception("SessionId is null"),
                Category = StoredMessageCategory.SubAgentAssistant,
                Content = content,
                Extra = JsonSerializer.Serialize(new AssistantMessageExtra{
                    ToolCalls = toolCalls
                }, StandardizedJsonOption.DefaultOptions),
                Model = _aiHubOption.DefaultModel,
                CreatedAt = DateTime.UtcNow,
                InputTokenCount = 0, // 这里其实是把 SubAgent 的 AssistantMessage 提上来，所以实际上没有消耗
                OutputTokenCount = 0, // 这里其实是把 SubAgent 的 AssistantMessage 提上来，所以实际上没有消耗
            };
            await dbContext.StoredMessages.AddAsync(subAgentAssistantMessage);
            await dbContext.SaveChangesAsync();

            await _chatHubWrapper.SendBlockMessage(messageId, content, toolCallsVo.ToList());
        }

    }

    public async Task<bool> ExecuteToolCallsIfAllConfirmed(CancellationToken cancellationToken)
    {
        var sessionId = _currentSubAgentSessionIdAccessor.GetSubAgentSessionId() ?? throw new Exception("SessionId is null");
        return await AgentRunnerContextHelper.ExecuteToolCallsIfAllConfirmed(_dbContextFactory, sessionId, _toolExecutor, cancellationToken);
    }

    /// <summary>
    /// 让 SubAgent 在某些情况下可以模拟用户的输入
    /// </summary>
    /// <param name="content"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task AddVirtualUserMessage(string content, CancellationToken cancellationToken)
    {
        var sessionId = _currentSubAgentSessionIdAccessor.GetSubAgentSessionId() ?? throw new Exception("SessionId is null");
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();


        var storedMessage = new StoredMessage{
            Id = Guid.CreateVersion7(),
            SessionId = sessionId,
            Category = StoredMessageCategory.User,
            Content = content,
            CreatedAt = DateTime.UtcNow
        };

        await dbContext.StoredMessages.AddAsync(storedMessage);
        await dbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<bool> IsSubAgnetJobDone(string content, List<AssistantMessageExtraToolCall> toolCalls, CancellationToken cancellationToken)
    {
        if (toolCalls.Any()){
            return false;
        }

        if(string.IsNullOrWhiteSpace(content)){
            
            var sessionId = _currentSubAgentSessionIdAccessor.GetSubAgentSessionId() ?? throw new Exception("SessionId is null");

            await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
            var storedMessage = new StoredMessage{
                Id = Guid.CreateVersion7(),
                SessionId = sessionId,
                Category = StoredMessageCategory.User,
                Content = "continue",
                CreatedAt = DateTime.UtcNow
            };
            await dbContext.StoredMessages.AddAsync(storedMessage);
            await dbContext.SaveChangesAsync(cancellationToken);

            return false;
        }

        return true;
    }
    
}
